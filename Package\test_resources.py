"""
Script pour tester si les ressources sont correctement incluses dans l'exécutable
"""

import os
import sys
from pathlib import Path

def test_resources():
    """Test si les ressources sont accessibles"""
    print("🔍 Test des ressources...")
    
    # Simuler l'environnement PyInstaller
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
        print(f"Mode PyInstaller détecté: {base_path}")
    except AttributeError:
        # Development mode
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"Mode développement: {base_path}")
    
    # Ressources à tester
    resources = [
        "Icone_App.png",
        "Icone_App_Sharp.ico", 
        "logo_Sofrecom.png"
    ]
    
    all_found = True
    
    for resource in resources:
        resource_path = os.path.join(base_path, resource)
        if os.path.exists(resource_path):
            size = os.path.getsize(resource_path)
            print(f"✅ {resource} - {size} bytes")
        else:
            print(f"❌ {resource} - MANQUANT")
            all_found = False
    
    return all_found

def test_icon_loading():
    """Test le chargement des icônes comme dans l'app"""
    print("\n🎨 Test du chargement d'icônes...")
    
    try:
        # Simuler get_resource_path
        try:
            base_path = sys._MEIPASS
        except AttributeError:
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Test icône Sharp
        sharp_ico = os.path.join(base_path, "Icone_App_Sharp.ico")
        original_icon = os.path.join(base_path, "Icone_App.png")
        
        if os.path.exists(sharp_ico):
            print(f"✅ Icône Sharp trouvée: {sharp_ico}")
            return sharp_ico
        elif os.path.exists(original_icon):
            print(f"✅ Icône PNG trouvée: {original_icon}")
            return original_icon
        else:
            print("❌ Aucune icône trouvée")
            return None
            
    except Exception as e:
        print(f"❌ Erreur test icône: {e}")
        return None

def test_logo_loading():
    """Test le chargement du logo"""
    print("\n🖼️ Test du chargement du logo...")
    
    try:
        try:
            base_path = sys._MEIPASS
        except AttributeError:
            base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        logo_path = os.path.join(base_path, "logo_Sofrecom.png")
        
        if os.path.exists(logo_path):
            print(f"✅ Logo trouvé: {logo_path}")
            
            # Test avec PIL si disponible
            try:
                from PIL import Image
                with Image.open(logo_path) as img:
                    print(f"✅ Logo chargeable: {img.size}")
                return True
            except ImportError:
                print("⚠️ PIL non disponible pour test")
                return True
            except Exception as e:
                print(f"❌ Erreur chargement logo: {e}")
                return False
        else:
            print("❌ Logo non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test logo: {e}")
        return False

def main():
    """Test principal"""
    print("🚀 Test des Ressources SofreTrack Pro")
    print("=" * 50)
    
    resources_ok = test_resources()
    icon_ok = test_icon_loading() is not None
    logo_ok = test_logo_loading()
    
    print("\n" + "=" * 50)
    
    if resources_ok and icon_ok and logo_ok:
        print("✅ TOUS LES TESTS PASSÉS")
        print("🎉 Les ressources sont correctement configurées")
        return True
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez la configuration PyInstaller")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\nAppuyez sur Entrée pour {'continuer' if success else 'quitter'}...")
    sys.exit(0 if success else 1)
