
import sys
import os
import tempfile

def test_resources():
    try:
        # Simuler get_resource_path comme dans l'app
        try:
            base_path = sys._MEIPASS
            print(f"PyInstaller mode - Base path: {base_path}")
        except AttributeError:
            print("Development mode - Resources not in bundle")
            return False

        # Tester les ressources
        resources = [
            "Icone_App.png",
            "Icone_App_Sharp.ico",
            "logo_Sofrecom.png"
        ]

        all_found = True
        for resource in resources:
            resource_path = os.path.join(base_path, resource)
            if os.path.exists(resource_path):
                size = os.path.getsize(resource_path)
                print(f"✅ {resource}: {size} bytes")
            else:
                print(f"❌ {resource}: NOT FOUND")
                all_found = False

        return all_found

    except Exception as e:
        print(f"Error testing resources: {e}")
        return False

if __name__ == "__main__":
    success = test_resources()
    sys.exit(0 if success else 1)
