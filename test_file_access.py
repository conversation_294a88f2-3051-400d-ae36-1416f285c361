#!/usr/bin/env python3
"""
Test script for file access checking functionality.
"""

import sys
import os
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from utils.file_utils import check_file_access, is_excel_file_open

def test_file_access():
    """Test the file access checking functionality."""
    print("🧪 Testing File Access Checking Functionality")
    print("=" * 50)
    
    # Test with a non-existent file
    print("\n1. Testing non-existent file:")
    test_file = "non_existent_file.xlsx"
    result = check_file_access(test_file, 'rw')
    print(f"   File: {test_file}")
    print(f"   Accessible: {result['accessible']}")
    print(f"   Error Type: {result['error_type']}")
    print(f"   User Message: {result['user_message']}")
    
    # Test with an existing file (create a temporary one)
    print("\n2. Testing accessible file:")
    temp_file = "temp_test.txt"
    try:
        with open(temp_file, 'w') as f:
            f.write("test content")
        
        result = check_file_access(temp_file, 'rw')
        print(f"   File: {temp_file}")
        print(f"   Accessible: {result['accessible']}")
        print(f"   Readable: {result['readable']}")
        print(f"   Writable: {result['writable']}")
        print(f"   Error Type: {result['error_type']}")
        
        # Clean up
        os.remove(temp_file)
        
    except Exception as e:
        print(f"   Error creating temp file: {e}")
    
    # Test Excel file open detection
    print("\n3. Testing Excel file open detection:")
    print("   (This function checks if a file appears to be locked)")
    
    # Test with non-existent file
    result = is_excel_file_open("non_existent.xlsx")
    print(f"   Non-existent file locked: {result}")
    
    # Test with accessible file
    try:
        with open(temp_file, 'w') as f:
            f.write("test")
        result = is_excel_file_open(temp_file)
        print(f"   Accessible file locked: {result}")
        os.remove(temp_file)
    except Exception as e:
        print(f"   Error testing file lock: {e}")
    
    print("\n✅ File access testing completed!")
    print("\n💡 To test with a real Excel file:")
    print("   1. Open an Excel file in Excel")
    print("   2. Run: check_file_access('path/to/file.xlsx', 'rw')")
    print("   3. You should see error_type='file_locked' and appropriate user message")

if __name__ == "__main__":
    test_file_access()
