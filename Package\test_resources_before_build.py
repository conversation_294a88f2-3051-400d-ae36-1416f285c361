"""
Script de test pour vérifier que toutes les ressources sont présentes avant le build
"""

import os
import sys
from pathlib import Path

def test_resources_before_build():
    """Teste que toutes les ressources nécessaires sont présentes"""
    print("TEST DES RESSOURCES AVANT BUILD")
    print("=" * 50)
    
    # Aller au répertoire parent (racine du projet)
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print(f"Répertoire du projet: {project_root}")
    print()
    
    # Ressources requises
    required_resources = {
        "Script principal": "src/main.py",
        "Icône Sharp (.ico)": "Icone_App_Sharp.ico", 
        "Icône PNG": "Icone_App.png",
        "Logo Sofrecom": "logo_Sofrecom.png"
    }
    
    all_present = True
    
    for description, relative_path in required_resources.items():
        full_path = project_root / relative_path
        
        if full_path.exists():
            file_size = full_path.stat().st_size
            if file_size > 0:
                print(f"✅ {description}")
                print(f"   Chemin: {relative_path}")
                print(f"   Taille: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                
                # Test spécial pour les images
                if relative_path.endswith(('.png', '.ico')):
                    try:
                        # Essayer d'ouvrir avec PIL pour vérifier l'intégrité
                        from PIL import Image
                        with Image.open(full_path) as img:
                            print(f"   Dimensions: {img.size[0]}x{img.size[1]}")
                            print(f"   Format: {img.format}")
                    except ImportError:
                        print("   (PIL non disponible pour vérifier l'image)")
                    except Exception as e:
                        print(f"   ⚠️  Erreur lecture image: {e}")
                        all_present = False
            else:
                print(f"❌ {description}: Fichier vide")
                print(f"   Chemin: {relative_path}")
                all_present = False
        else:
            print(f"❌ {description}: MANQUANT")
            print(f"   Chemin attendu: {relative_path}")
            all_present = False
        
        print()
    
    # Test des imports critiques
    print("TEST DES IMPORTS CRITIQUES")
    print("-" * 30)
    
    critical_imports = [
        "tkinter",
        "PIL", 
        "pandas",
        "openpyxl"
    ]
    
    for module_name in critical_imports:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            all_present = False
    
    print()
    print("=" * 50)
    
    if all_present:
        print("🎉 TOUS LES TESTS PASSÉS - PRÊT POUR LE BUILD")
        return True
    else:
        print("❌ ÉCHEC - CORRIGEZ LES ERREURS AVANT LE BUILD")
        return False

def test_file_utils_paths():
    """Teste que les chemins dans file_utils.py correspondent aux fichiers réels"""
    print("\nTEST DES CHEMINS DANS FILE_UTILS")
    print("-" * 40)
    
    try:
        # Ajouter src au path pour importer
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        src_path = project_root / "src"
        
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from utils.file_utils import get_logo_path, get_icon_path, get_resource_path
        
        # Tester get_logo_path
        logo_path = get_logo_path()
        expected_logo = project_root / "logo_Sofrecom.png"
        
        print(f"Logo path from file_utils: {logo_path}")
        print(f"Expected logo path: {expected_logo}")
        
        if expected_logo.exists():
            print("✅ Logo path correct")
        else:
            print("❌ Logo path incorrect")
            return False
        
        # Tester get_icon_path  
        icon_path = get_icon_path()
        print(f"Icon path from file_utils: {icon_path}")
        
        if Path(icon_path).exists():
            print("✅ Icon path correct")
        else:
            print("❌ Icon path incorrect")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur test file_utils: {e}")
        return False

if __name__ == "__main__":
    success1 = test_resources_before_build()
    success2 = test_file_utils_paths()
    
    if success1 and success2:
        print("\n🚀 PRÊT POUR LE BUILD!")
        sys.exit(0)
    else:
        print("\n🛑 CORRIGEZ LES ERREURS AVANT DE CONTINUER")
        sys.exit(1)
