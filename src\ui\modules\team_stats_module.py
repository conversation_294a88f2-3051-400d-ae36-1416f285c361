"""
Team Statistics module - Dashboard for analyzing team performance and statistics.
"""

import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import logging
import os
from typing import Optional, List, Dict, Any
from pathlib import Path
import sys

# Ensure src directory is in path
src_path = Path(__file__).parent.parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from config.constants import COLORS, UIConfig
from core import FileProcessor, DataValidator, ExcelGenerator
from utils.file_utils import get_icon_path, check_file_access, is_excel_file_open
from utils.lazy_imports import get_pandas
from utils.performance import run_async_task
from utils.session_manager import SessionManager, AutoSaveManager
from ui.styles import StyleManager, create_card_frame, create_section_header
from ui.keyboard_shortcuts import KeyboardShortcutManager

logger = logging.getLogger(__name__)


class TeamStatsModule:
    """Team Statistics module for analyzing team performance and statistics."""
    
    def __init__(self, parent: tk.Widget, navigation_manager=None):
        """
        Initialize the Team Statistics module.

        Args:
            parent: Parent widget
            navigation_manager: Navigation manager instance
        """
        self.parent = parent
        self.navigation_manager = navigation_manager
        self.logger = logging.getLogger(__name__)

        # Initialize core components
        try:
            self.file_processor = FileProcessor()
            self.data_validator = DataValidator()
            self.excel_generator = ExcelGenerator()
            self.logger.info("Core components initialized")
        except Exception as e:
            self.logger.error(f"Error initializing core components: {e}")
            self.file_processor = None
            self.data_validator = None
            self.excel_generator = None

        # Module data
        self.global_suivi_data = None
        self.team_statistics = {}
        self.collaborator_stats = {}
        self.ticket_status_breakdown = {}
        self.overall_averages = {}
        
        # Get dynamic Teams path for current user
        from config.constants import TeamsConfig
        self.teams_folder_path = TeamsConfig.get_global_teams_path()
        self.global_excel_filename = "Suivis Global Tickets CMS Adr_PA.xlsx"

        # UI components
        self.progress_var = None
        self.progress_bar = None
        self.status_label = None
        self.load_button = None
        self.stats_display = None
        self.refresh_button = None
        self.export_buttons = None
        self.export_filters = None

        # Export filter variables
        self.collab_var = None
        self.month_var = None
        self.year_var = None
        self.collab_combo = None
        self.month_combo = None
        self.year_combo = None

        # Session management (optional)
        self.session_manager = None
        self.auto_save_manager = None
        self.keyboard_manager = None

        # Create UI first
        self._create_module_ui()

        # Initialize optional features after UI is created
        self.parent.after(100, self._initialize_optional_features)

    def _initialize_optional_features(self):
        """Initialize optional features after UI is ready."""
        try:
            # Session management
            self.session_manager = SessionManager("sessions/team_stats")
            self.auto_save_manager = AutoSaveManager(self.session_manager)

            # Setup keyboard shortcuts
            if self.navigation_manager and hasattr(self.navigation_manager, 'root'):
                self.keyboard_manager = KeyboardShortcutManager(self.navigation_manager.root)
                self._setup_module_shortcuts()

            # Restore session
            self._restore_session()

            # Start auto-save
            if self.auto_save_manager and self.navigation_manager and hasattr(self.navigation_manager, 'root'):
                self.auto_save_manager.start_auto_save(self.navigation_manager.root)

            self.logger.info("Optional features initialized successfully")

        except Exception as e:
            self.logger.error(f"Error initializing optional features: {e}")

    def _create_module_ui(self):
        """Create the module user interface."""
        try:
            # Module title bar
            self._create_title_bar()

            # Main content area
            self._create_content_area()

            self.logger.info("Module UI created successfully")

        except Exception as e:
            self.logger.error(f"Error creating module UI: {e}")
            self._create_error_ui(str(e))

    def _create_error_ui(self, error_message: str):
        """Create a simple error display when module fails to load."""
        error_frame = tk.Frame(self.parent, bg=COLORS['BG'])
        error_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Error icon and message
        error_label = tk.Label(
            error_frame,
            text=f"❌ Erreur lors du chargement du module:\n\n{error_message}",
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['DANGER'],
            bg=COLORS['BG'],
            justify=tk.CENTER
        )
        error_label.pack(expand=True)

        # Retry button
        retry_btn = tk.Button(
            error_frame,
            text="🔄 Réessayer",
            command=self._retry_initialization,
            bg=COLORS['PRIMARY'],
            fg='white',
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=20,
            pady=10
        )
        retry_btn.pack(pady=10)

    def _retry_initialization(self):
        """Retry module initialization."""
        try:
            # Clear the parent
            for widget in self.parent.winfo_children():
                widget.destroy()

            # Reinitialize
            self.__init__(self.parent, self.navigation_manager)

        except Exception as e:
            self.logger.error(f"Retry failed: {e}")
            messagebox.showerror("Erreur", f"Impossible de réinitialiser le module:\n{e}")

    def _create_title_bar(self):
        """Create the module title bar."""
        title_frame = tk.Frame(self.parent, bg=COLORS['CARD'], height=60)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))
        title_frame.pack_propagate(False)
        
        # Title content
        title_content = tk.Frame(title_frame, bg=COLORS['CARD'])
        title_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # Module icon and title
        title_left = tk.Frame(title_content, bg=COLORS['CARD'])
        title_left.pack(side=tk.LEFT, fill=tk.Y)
        
        # Icon
        icon_label = tk.Label(
            title_left,
            text="📊",
            font=("Segoe UI", 20),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # Title and description
        title_text_frame = tk.Frame(title_left, bg=COLORS['CARD'])
        title_text_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        title_label = tk.Label(
            title_text_frame,
            text="Statistiques Équipe",
            font=UIConfig.FONT_HEADER,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(
            title_text_frame,
            text="Tableau de bord des performances de l'équipe",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        subtitle_label.pack(anchor=tk.W)
        
        # Action buttons on the right
        title_right = tk.Frame(title_content, bg=COLORS['CARD'])
        title_right.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Help button
        help_btn = ttk.Button(
            title_right,
            text="❓ Aide",
            command=self._show_help,
            style='Compact.TButton'
        )
        help_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Reset button
        reset_btn = ttk.Button(
            title_right,
            text="🔄 Réinitialiser",
            command=self._reset_module,
            style='CompactWarning.TButton'
        )
        reset_btn.pack(side=tk.RIGHT, padx=(5, 0))

    def _create_content_area(self):
        """Create the main content area."""
        # Content container
        content_container = tk.Frame(self.parent, bg=COLORS['BG'])
        content_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Configure columns for 3-column layout
        content_container.grid_columnconfigure(0, weight=0, minsize=220)  # Left column - data loading & overview
        content_container.grid_columnconfigure(1, weight=1, minsize=350)  # Middle column - statistics (reduced)
        content_container.grid_columnconfigure(2, weight=0, minsize=200)  # Right column - export stats
        
        # Left column - Data loading and overview
        left_column = tk.Frame(content_container, bg=COLORS['BG'])
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 3))

        # Middle column - Statistics display (reduced width)
        middle_column = tk.Frame(content_container, bg=COLORS['BG'])
        middle_column.grid(row=0, column=1, sticky="nsew", padx=3)

        # Right column - Export Stats
        right_column = tk.Frame(content_container, bg=COLORS['BG'])
        right_column.grid(row=0, column=2, sticky="nsew", padx=(3, 0))

        # Create sections
        self._create_data_loading_section(left_column)
        self._create_overview_section(left_column)
        self._create_statistics_section(middle_column)
        self._create_export_section(right_column)

    def _setup_module_shortcuts(self):
        """Set up keyboard shortcuts specific to this module."""
        self.keyboard_manager.set_callback("Control-l", self._load_shortcut)
        self.keyboard_manager.set_callback("Control-r", self._refresh_shortcut)
        self.keyboard_manager.set_callback("F5", self._refresh_shortcut)

    def _show_help(self):
        """Show help information for this module."""
        help_text = "Module de statistiques d'équipe - Analyse des KPIs et performances."
        messagebox.showinfo("Aide - Statistiques Équipe", help_text)

    def _reset_module(self):
        """Reset the module to initial state."""
        try:
            self.global_suivi_data = None
            self.team_statistics.clear()
            self.collaborator_stats.clear()
            self.ticket_status_breakdown.clear()
            self.overall_averages.clear()

            if self.stats_display:
                # Clear statistics display
                for widget in self.stats_display.winfo_children():
                    widget.destroy()

            if self.status_label:
                self.status_label.config(text="Prêt à charger les données")

            if self.progress_var:
                self.progress_var.set(0)

            if self.load_button:
                self.load_button.config(state=tk.NORMAL)

            self._disable_export_buttons()

            self.logger.info("Module reset successfully")

        except Exception as e:
            self.logger.error(f"Error resetting module: {e}")
            messagebox.showerror("Erreur", f"Erreur lors de la réinitialisation:\n{e}")

    def _create_data_loading_section(self, parent: tk.Widget):
        """Create the compact data loading section."""
        # Loading card - more compact
        load_card = create_card_frame(parent)
        load_card.pack(fill=tk.X, pady=(0, 8))

        # Section header - reduced padding
        header_frame = create_section_header(load_card, "📂", "Chargement des données")
        header_frame.pack(fill=tk.X, padx=12, pady=(10, 5))

        # Content frame - reduced padding
        content_frame = tk.Frame(load_card, bg=COLORS['CARD'])
        content_frame.pack(fill=tk.X, padx=12, pady=(0, 10))

        # Horizontal layout for button and progress
        control_frame = tk.Frame(content_frame, bg=COLORS['CARD'])
        control_frame.pack(fill=tk.X)

        # Load button - smaller
        self.load_button = tk.Button(
            control_frame,
            text="📂 Charger",
            command=self._load_global_data,
            bg=COLORS['SUCCESS'],
            fg='white',
            font=UIConfig.FONT_SMALL,
            relief='flat',
            padx=15,
            pady=5
        )
        self.load_button.pack(side=tk.LEFT)

        # Progress bar - inline with button
        progress_frame = tk.Frame(control_frame, bg=COLORS['CARD'])
        progress_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=3)

        # Status label - smaller
        self.status_label = tk.Label(
            content_frame,
            text="Prêt à charger les données",
            font=("Segoe UI", 6),
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        self.status_label.pack(anchor=tk.W, pady=(3, 0))

        # File status indicator
        status_frame = tk.Frame(content_frame, bg=COLORS['CARD'])
        status_frame.pack(fill=tk.X, pady=(5, 0))

        status_icon = tk.Label(
            status_frame,
            text="📁",
            font=("Segoe UI", 8),
            bg=COLORS['CARD'],
            fg=COLORS['INFO']
        )
        status_icon.pack(side=tk.LEFT, padx=(0, 3))

        self.file_status_label = tk.Label(
            status_frame,
            text="Vérification du statut du fichier...",
            font=("Segoe UI", 6),
            bg=COLORS['CARD'],
            fg=COLORS['INFO']
        )
        self.file_status_label.pack(side=tk.LEFT)

        # Refresh status button
        refresh_status_btn = tk.Button(
            status_frame,
            text="🔄",
            command=self._update_file_status_indicator,
            bg=COLORS['BORDER'],
            fg=COLORS['TEXT_SECONDARY'],
            font=("Segoe UI", 6),
            relief='flat',
            padx=3,
            pady=1
        )
        refresh_status_btn.pack(side=tk.RIGHT)

        # Initialize file status
        self._update_file_status_indicator()

    def _create_overview_section(self, parent: tk.Widget):
        """Create the overview section."""
        # Overview card
        overview_card = create_card_frame(parent)
        overview_card.pack(fill=tk.X, pady=(0, 10))

        # Section header
        header_frame = create_section_header(overview_card, "📈", "Aperçu général")
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Content frame
        self.overview_content = tk.Frame(overview_card, bg=COLORS['CARD'])
        self.overview_content.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Initial message
        initial_label = tk.Label(
            self.overview_content,
            text="Chargez les données pour voir l'aperçu général",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        initial_label.pack(anchor=tk.W)

    def _create_statistics_section(self, parent: tk.Widget):
        """Create the detailed statistics section."""
        # Statistics card
        stats_card = create_card_frame(parent)
        stats_card.pack(fill=tk.BOTH, expand=True)

        # Section header with refresh button
        header_frame = tk.Frame(stats_card, bg=COLORS['CARD'])
        header_frame.pack(fill=tk.X, padx=15, pady=(10, 8))  # Reduced top padding

        # Header icon and title
        header_icon = tk.Label(
            header_frame,
            text="📊",
            font=("Segoe UI", 12),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        header_icon.pack(side=tk.LEFT, padx=(0, 8))

        header_label = tk.Label(
            header_frame,
            text="Statistiques détaillées",
            font=UIConfig.FONT_HEADER,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        header_label.pack(side=tk.LEFT)

        # Refresh button
        self.refresh_button = tk.Button(
            header_frame,
            text="🔄 Actualiser",
            command=self._refresh_statistics,
            bg=COLORS['SECONDARY'],
            fg='white',
            font=UIConfig.FONT_SMALL,
            relief='flat',
            padx=10,
            pady=3,
            state=tk.DISABLED
        )
        self.refresh_button.pack(side=tk.RIGHT)

        # Statistics display area with scrollbar
        stats_frame = tk.Frame(stats_card, bg=COLORS['CARD'])
        stats_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 10))  # Reduced bottom padding

        # Create scrollable frame with mouse wheel support
        canvas = tk.Canvas(stats_frame, bg=COLORS['CARD'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(stats_frame, orient="vertical", command=canvas.yview)
        self.stats_display = tk.Frame(canvas, bg=COLORS['CARD'])

        self.stats_display.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.stats_display, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Add mouse wheel scrolling support
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', _bind_mousewheel)
        canvas.bind('<Leave>', _unbind_mousewheel)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initial message
        initial_stats_label = tk.Label(
            self.stats_display,
            text="Chargez les données pour voir les statistiques détaillées",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['CARD']
        )
        initial_stats_label.pack(anchor=tk.W, pady=10)  # Reduced padding

    def _create_export_section(self, parent: tk.Widget):
        """Create the export statistics section."""
        # Export card - no top expansion to reduce blank space
        export_card = create_card_frame(parent)
        export_card.pack(fill=tk.X, anchor=tk.N)  # Changed from fill=tk.BOTH, expand=True

        # Section header
        header_frame = create_section_header(export_card, "📤", "Export Stat")
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Content frame
        export_content = tk.Frame(export_card, bg=COLORS['CARD'])
        export_content.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Export buttons
        self._create_export_buttons(export_content)

    def _create_export_buttons(self, parent: tk.Widget):
        """Create export filters and buttons."""
        # Filters section
        filters_frame = tk.Frame(parent, bg=COLORS['CARD'])
        filters_frame.pack(fill=tk.X, pady=(0, 10))

        # Collaborator filter
        collab_label = tk.Label(
            filters_frame,
            text="Collaborateur:",
            font=("Segoe UI", 8, "bold"),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        collab_label.pack(anchor=tk.W, pady=(0, 2))

        self.collab_var = tk.StringVar()
        self.collab_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.collab_var,
            font=("Segoe UI", 8),
            state="readonly",
            width=18
        )
        self.collab_combo.pack(fill=tk.X, pady=(0, 8))

        # Month filter
        month_label = tk.Label(
            filters_frame,
            text="Mois:",
            font=("Segoe UI", 8, "bold"),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        month_label.pack(anchor=tk.W, pady=(0, 2))

        self.month_var = tk.StringVar()
        self.month_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.month_var,
            font=("Segoe UI", 8),
            state="readonly",
            width=18,
            values=["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                   "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
        )
        self.month_combo.pack(fill=tk.X, pady=(0, 8))

        # Year filter
        year_label = tk.Label(
            filters_frame,
            text="Année:",
            font=("Segoe UI", 8, "bold"),
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        year_label.pack(anchor=tk.W, pady=(0, 2))

        from datetime import datetime
        current_year = datetime.now().year
        self.year_var = tk.StringVar(value=str(current_year))
        self.year_combo = ttk.Combobox(
            filters_frame,
            textvariable=self.year_var,
            font=("Segoe UI", 8),
            state="readonly",
            width=18,
            values=[str(year) for year in range(current_year-2, current_year+2)]
        )
        self.year_combo.pack(fill=tk.X, pady=(0, 10))

        # Export button
        excel_btn = tk.Button(
            parent,
            text="📊 Export CTJ Excel",
            command=self._export_ctj_to_excel,
            bg=COLORS['SUCCESS'],
            fg='white',
            font=UIConfig.FONT_SMALL,
            relief='flat',
            padx=15,
            pady=8,
            state=tk.DISABLED
        )
        excel_btn.pack(fill=tk.X)

        # Store components for enabling/disabling
        self.export_buttons = {'excel': excel_btn}
        self.export_filters = {
            'collaborator': self.collab_combo,
            'month': self.month_combo,
            'year': self.year_combo
        }

    def _load_global_data(self):
        """Load and analyze data from the global suivi file."""
        try:
            self.status_label.config(text="Chargement du fichier global...")
            self.progress_var.set(10)
            self.load_button.config(state=tk.DISABLED)

            # Construct path to global file
            global_file_path = os.path.join(self.teams_folder_path, self.global_excel_filename)

            if not os.path.exists(global_file_path):
                messagebox.showerror(
                    "Fichier non trouvé",
                    f"Le fichier global n'a pas été trouvé:\n{global_file_path}\n\n"
                    "Assurez-vous que le module 'Suivi Global Tickets' a été exécuté au moins une fois."
                )
                self._reset_loading_state()
                return

            # Check file access before proceeding
            self.status_label.config(text="Vérification de l'accès au fichier...")
            self.progress_var.set(20)

            access_result = check_file_access(global_file_path, 'r')
            if not access_result['accessible']:
                self.logger.warning(f"File access issue: {access_result['error_message']}")

                # Show user-friendly dialog and get retry decision
                if access_result['error_type'] in ['file_locked', 'permission_denied']:
                    # This is a file-in-use situation, show the custom dialog
                    retry = self._show_file_access_dialog(access_result, global_file_path)
                    if not retry:
                        self._reset_loading_state()
                        return

                    # User wants to retry, check again
                    retry_access = check_file_access(global_file_path, 'r')
                    if not retry_access['accessible']:
                        # Still not accessible, show warning and abort
                        messagebox.showwarning(
                            "Fichier toujours en cours d'utilisation",
                            f"{retry_access['user_message']}\n\nVeuillez fermer Excel et cliquer sur 'Charger les données' pour réessayer."
                        )
                        self._reset_loading_state()
                        return
                else:
                    # Other types of errors, show error and abort
                    messagebox.showerror("Erreur d'accès au fichier", access_result['user_message'])
                    self._reset_loading_state()
                    return

            self.status_label.config(text="Lecture des données Excel...")
            self.progress_var.set(30)

            # Load Excel data using pandas
            pd = get_pandas()

            # Read all sheets
            excel_data = {}
            sheet_names = ['Suivi Tickets', 'Traitement CMS Adr', 'Traitement PA']

            for i, sheet_name in enumerate(sheet_names):
                try:
                    self.status_label.config(text=f"Lecture de la page '{sheet_name}'...")
                    self.progress_var.set(30 + (i * 20))

                    df = pd.read_excel(global_file_path, sheet_name=sheet_name)
                    excel_data[sheet_name] = df
                    self.logger.info(f"Loaded sheet '{sheet_name}' with {len(df)} rows")

                except Exception as e:
                    self.logger.warning(f"Could not load sheet '{sheet_name}': {e}")
                    excel_data[sheet_name] = pd.DataFrame()

            self.global_suivi_data = excel_data

            self.status_label.config(text="Analyse des statistiques...")
            self.progress_var.set(80)

            # Analyze the data
            self._analyze_team_statistics()

            self.status_label.config(text="Mise à jour de l'affichage...")
            self.progress_var.set(90)

            # Update UI
            self._update_overview_display()
            self._update_statistics_display()

            self.progress_var.set(100)
            self.status_label.config(text="Données chargées avec succès")
            self.refresh_button.config(state=tk.NORMAL)
            self._enable_export_buttons()
            self._update_export_filters()

            # Update file status indicator
            self._update_file_status_indicator()

            self.logger.info("Global data loaded and analyzed successfully")

        except Exception as e:
            self.logger.error(f"Error loading global data: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données:\n{e}")
            self._reset_loading_state()

    def _reset_loading_state(self):
        """Reset the loading state after error or completion."""
        self.load_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.status_label.config(text="Prêt à charger les données")

    def _show_file_access_dialog(self, access_result: dict, file_path: str) -> bool:
        """
        Show a user-friendly dialog for file access issues.

        Args:
            access_result: Result from check_file_access()
            file_path: Path to the file

        Returns:
            True if user wants to retry, False otherwise
        """
        filename = os.path.basename(file_path)

        # Create custom dialog
        dialog = tk.Toplevel(self.parent)
        dialog.title("Fichier en cours d'utilisation")
        dialog.geometry("500x400")
        dialog.configure(bg=COLORS['BG'])
        dialog.resizable(False, False)

        # Center the dialog
        dialog.transient(self.parent.winfo_toplevel())
        dialog.grab_set()

        # Main frame
        main_frame = tk.Frame(dialog, bg=COLORS['BG'], padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Icon and title
        title_frame = tk.Frame(main_frame, bg=COLORS['BG'])
        title_frame.pack(fill=tk.X, pady=(0, 15))

        icon_label = tk.Label(
            title_frame,
            text="🔒",
            font=("Segoe UI", 24),
            bg=COLORS['BG'],
            fg=COLORS['WARNING']
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 10))

        title_label = tk.Label(
            title_frame,
            text="Fichier Excel en cours d'utilisation",
            font=UIConfig.FONT_HEADER,
            bg=COLORS['BG'],
            fg=COLORS['PRIMARY']
        )
        title_label.pack(side=tk.LEFT)

        # Message
        message_text = access_result.get('user_message', f"Le fichier '{filename}' est actuellement utilisé.")
        message_label = tk.Label(
            main_frame,
            text=message_text,
            font=UIConfig.FONT_SUBTITLE,
            bg=COLORS['BG'],
            fg=COLORS['TEXT_SECONDARY'],
            wraplength=450,
            justify=tk.LEFT
        )
        message_label.pack(fill=tk.X, pady=(0, 15))

        # Suggestions
        suggestions = access_result.get('suggestions', [])
        if suggestions:
            suggestions_label = tk.Label(
                main_frame,
                text="💡 Solutions recommandées:",
                font=UIConfig.FONT_SUBTITLE,
                bg=COLORS['BG'],
                fg=COLORS['PRIMARY']
            )
            suggestions_label.pack(anchor=tk.W, pady=(0, 5))

            for i, suggestion in enumerate(suggestions, 1):
                suggestion_label = tk.Label(
                    main_frame,
                    text=f"  {i}. {suggestion}",
                    font=UIConfig.FONT_SMALL,
                    bg=COLORS['BG'],
                    fg=COLORS['TEXT_SECONDARY'],
                    wraplength=450,
                    justify=tk.LEFT
                )
                suggestion_label.pack(anchor=tk.W, pady=1)

        # Additional info
        info_frame = tk.Frame(main_frame, bg=COLORS['LIGHT'], relief=tk.RAISED, bd=1)
        info_frame.pack(fill=tk.X, pady=(15, 0))

        info_label = tk.Label(
            info_frame,
            text="ℹ️ Astuce: Vous pouvez consulter le fichier Excel pendant que l'application fonctionne,\nmais fermez-le avant de charger les données pour les statistiques.",
            font=UIConfig.FONT_SMALL,
            bg=COLORS['LIGHT'],
            fg=COLORS['INFO'],
            justify=tk.CENTER,
            padx=10,
            pady=8
        )
        info_label.pack()

        # Buttons
        button_frame = tk.Frame(main_frame, bg=COLORS['BG'])
        button_frame.pack(fill=tk.X, pady=(20, 0))

        result = {'retry': False}

        def on_retry():
            result['retry'] = True
            dialog.destroy()

        def on_cancel():
            result['retry'] = False
            dialog.destroy()

        # Retry button
        retry_btn = tk.Button(
            button_frame,
            text="🔄 Réessayer",
            command=on_retry,
            bg=COLORS['PRIMARY'],
            fg='white',
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=20,
            pady=8
        )
        retry_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Cancel button
        cancel_btn = tk.Button(
            button_frame,
            text="❌ Annuler",
            command=on_cancel,
            bg=COLORS['BORDER'],
            fg=COLORS['TEXT_SECONDARY'],
            font=UIConfig.FONT_BUTTON,
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side=tk.RIGHT)

        # Wait for dialog to close
        dialog.wait_window()

        return result['retry']

    def _check_global_file_status(self):
        """Check if the global Excel file is accessible and update UI accordingly."""
        try:
            file_path = os.path.join(self.teams_folder_path, self.global_excel_filename)

            if not os.path.exists(file_path):
                return "not_exists"

            if is_excel_file_open(file_path):
                return "in_use"

            access_result = check_file_access(file_path, 'r')  # Only need read access for stats
            if access_result['accessible']:
                return "available"
            else:
                return "locked"

        except Exception as e:
            self.logger.error(f"Error checking global file status: {e}")
            return "unknown"

    def _update_file_status_indicator(self):
        """Update the file status indicator in the UI."""
        try:
            status = self._check_global_file_status()

            if hasattr(self, 'file_status_label'):
                status_config = {
                    'not_exists': {'text': '📄 Fichier global non trouvé', 'color': COLORS['WARNING']},
                    'available': {'text': '✅ Fichier disponible', 'color': COLORS['SUCCESS']},
                    'in_use': {'text': '🔒 Fichier ouvert dans Excel', 'color': COLORS['WARNING']},
                    'locked': {'text': '⚠️ Fichier verrouillé', 'color': COLORS['DANGER']},
                    'unknown': {'text': '❓ Statut inconnu', 'color': COLORS['TEXT_MUTED']}
                }

                config = status_config.get(status, status_config['unknown'])
                self.file_status_label.config(text=config['text'], fg=config['color'])

        except Exception as e:
            self.logger.error(f"Error updating file status indicator: {e}")

    def _analyze_team_statistics(self):
        """Analyze team statistics from the loaded data."""
        try:
            if not self.global_suivi_data:
                return

            pd = get_pandas()
            from datetime import datetime

            # Initialize statistics with KPI focus
            self.team_statistics = {
                'total_tickets': 0,
                'total_cms_records': 0,
                'total_pa_records': 0,
                'total_duration_cms': 0,
                'total_duration_pa': 0,
                'total_duration_finale': 0,
                'collaborators': set(),
                'communes': set(),
                'communes_traitees_mois_courant': 0,
                'communes_autres_statuts': {},
                # New KPI metrics
                'team_dmt': 0,  # Team-wide DMT (Average Treatment Duration)
                'team_ctj_today': 0,  # Team-wide CTJ for today (Daily Treatment Capacity)
                'total_elements_today': 0  # Total elements processed today by all collaborators
            }

            self.collaborator_stats = {}
            self.ticket_status_breakdown = {}
            self.overall_averages = {}

            # Analyze Page 1 (Suivi Tickets) - Main data source
            if 'Suivi Tickets' in self.global_suivi_data:
                df_tickets = self.global_suivi_data['Suivi Tickets']

                if not df_tickets.empty:
                    self.team_statistics['total_tickets'] = len(df_tickets)

                    # Extract collaborators and communes
                    if 'Collaborateur' in df_tickets.columns:
                        collaborators = df_tickets['Collaborateur'].dropna().unique()
                        self.team_statistics['collaborators'].update(collaborators)

                    if 'Nom Commune' in df_tickets.columns:
                        communes = df_tickets['Nom Commune'].dropna().unique()
                        self.team_statistics['communes'].update(communes)

                    # Analyze communes by status and date
                    self._analyze_commune_status_by_date(df_tickets, pd, datetime)

                    # Analyze ticket status breakdown (keep existing logic for compatibility)
                    if 'STATUT Ticket' in df_tickets.columns:
                        status_counts = df_tickets['STATUT Ticket'].value_counts()
                        self.ticket_status_breakdown = status_counts.to_dict()
                    elif 'Etat' in df_tickets.columns:
                        status_counts = df_tickets['Etat'].value_counts()
                        self.ticket_status_breakdown = status_counts.to_dict()

                    # Calculate total "Durée Finale" if available
                    if 'Durée Finale' in df_tickets.columns:
                        finale_duration = self._calculate_duration_sum(df_tickets['Durée Finale'])
                        self.team_statistics['total_duration_finale'] = finale_duration

                    # Analyze by collaborator
                    if 'Collaborateur' in df_tickets.columns:
                        for collaborator in df_tickets['Collaborateur'].dropna().unique():
                            collab_data = df_tickets[df_tickets['Collaborateur'] == collaborator]
                            collab_communes = set(collab_data['Nom Commune'].dropna().unique()) if 'Nom Commune' in collab_data.columns else set()

                            # Calculate DMT (Durée Moyenne de Traitement) for this collaborator
                            # Only for communes with status "Traité"
                            dmt_collaborator = self._calculate_dmt_for_treated_communes(collab_data, pd)

                            # Calculate CTJ (Capacité de Traitement Journalier) for today
                            # From page 3 (Traitement PA) column G
                            ctj_today = self._calculate_ctj_from_page3(collaborator, pd, datetime)

                            self.collaborator_stats[collaborator] = {
                                'tickets_count': len(collab_data),
                                'cms_records': 0,
                                'pa_records': 0,
                                'cms_duration': 0,
                                'pa_duration': 0,
                                'avg_cms_duration': 0,
                                'avg_pa_duration': 0,
                                'avg_finale_duration': dmt_collaborator,  # This is now the DMT
                                'communes': collab_communes,
                                'commune_count': len(collab_communes),
                                # New KPI metrics
                                'dmt': dmt_collaborator,  # DMT - Average Treatment Duration
                                'ctj_today': ctj_today,   # CTJ - Daily Treatment Capacity for today
                                'elements_today': ctj_today  # Elements processed today (same as CTJ for now)
                            }

            # Analyze Page 2 (Traitement CMS Adr) - Duration data
            if 'Traitement CMS Adr' in self.global_suivi_data:
                df_cms = self.global_suivi_data['Traitement CMS Adr']

                if not df_cms.empty:
                    self.team_statistics['total_cms_records'] = len(df_cms)

                    # Calculate total CMS duration
                    if 'Durée' in df_cms.columns:
                        duration_sum = self._calculate_duration_sum(df_cms['Durée'])
                        self.team_statistics['total_duration_cms'] = duration_sum

                    # Add CMS data to collaborator stats
                    if 'Collaborateur' in df_cms.columns:
                        for collaborator in df_cms['Collaborateur'].dropna().unique():
                            if collaborator in self.collaborator_stats:
                                collab_cms = df_cms[df_cms['Collaborateur'] == collaborator]
                                self.collaborator_stats[collaborator]['cms_records'] = len(collab_cms)

                                if 'Durée' in collab_cms.columns:
                                    duration_sum = self._calculate_duration_sum(collab_cms['Durée'])
                                    self.collaborator_stats[collaborator]['cms_duration'] = duration_sum

                                    # Calculate average CMS duration per commune
                                    commune_count = self.collaborator_stats[collaborator]['commune_count']
                                    if commune_count > 0:
                                        self.collaborator_stats[collaborator]['avg_cms_duration'] = duration_sum / commune_count

            # Analyze Page 3 (Traitement PA) - Duration data
            if 'Traitement PA' in self.global_suivi_data:
                df_pa = self.global_suivi_data['Traitement PA']

                if not df_pa.empty:
                    self.team_statistics['total_pa_records'] = len(df_pa)

                    # Calculate total PA duration
                    if 'Durée' in df_pa.columns:
                        duration_sum = self._calculate_duration_sum(df_pa['Durée'])
                        self.team_statistics['total_duration_pa'] = duration_sum

                    # Add PA data to collaborator stats
                    if 'Collaborateur' in df_pa.columns:
                        for collaborator in df_pa['Collaborateur'].dropna().unique():
                            if collaborator in self.collaborator_stats:
                                collab_pa = df_pa[df_pa['Collaborateur'] == collaborator]
                                self.collaborator_stats[collaborator]['pa_records'] = len(collab_pa)

                                if 'Durée' in collab_pa.columns:
                                    duration_sum = self._calculate_duration_sum(collab_pa['Durée'])
                                    self.collaborator_stats[collaborator]['pa_duration'] = duration_sum

                                    # Calculate average PA duration per commune
                                    commune_count = self.collaborator_stats[collaborator]['commune_count']
                                    if commune_count > 0:
                                        self.collaborator_stats[collaborator]['avg_pa_duration'] = duration_sum / commune_count

            # Calculate overall team averages and KPIs
            self._calculate_overall_averages()
            self._calculate_team_kpis()

            self.logger.info(f"Analyzed statistics for {len(self.collaborator_stats)} collaborators")

        except Exception as e:
            self.logger.error(f"Error analyzing team statistics: {e}")
            raise

    def _calculate_duration_sum(self, duration_series):
        """Calculate sum of duration values, handling different formats."""
        try:
            total_minutes = 0

            for duration in duration_series.dropna():
                if isinstance(duration, (int, float)):
                    # Assume it's already in minutes
                    total_minutes += duration
                elif isinstance(duration, str):
                    # Try to parse time format (HH:MM or MM)
                    duration = duration.strip()
                    if ':' in duration:
                        parts = duration.split(':')
                        if len(parts) == 2:
                            hours = int(parts[0])
                            minutes = int(parts[1])
                            total_minutes += (hours * 60) + minutes
                    else:
                        # Assume it's minutes
                        try:
                            total_minutes += float(duration)
                        except ValueError:
                            continue

            return total_minutes

        except Exception as e:
            self.logger.error(f"Error calculating duration sum: {e}")
            return 0

    def _calculate_dmt_for_treated_communes(self, collab_data, pd):
        """Calculate DMT only for communes with status 'Traité'."""
        try:
            # Find the correct status column (handle variations)
            status_column = None
            for col in collab_data.columns:
                if 'etat ticket pa' in col.lower().strip():
                    status_column = col
                    break

            if status_column is None:
                # Fallback to other possible status columns
                for col in collab_data.columns:
                    if any(keyword in col.lower() for keyword in ['etat', 'statut', 'status']):
                        status_column = col
                        break

            if status_column is None or 'Durée Finale' not in collab_data.columns:
                self.logger.warning(f"Status column or Durée Finale not found. Available columns: {list(collab_data.columns)}")
                return 0

            # Filter data for communes with status "Traité"
            treated_data = collab_data[collab_data[status_column].astype(str).str.strip() == 'Traité']

            if treated_data.empty:
                return 0

            # Calculate total duration for treated communes
            total_duration = self._calculate_duration_sum(treated_data['Durée Finale'])
            treated_count = len(treated_data)

            # Return average duration per treated commune
            return total_duration / treated_count if treated_count > 0 else 0

        except Exception as e:
            self.logger.error(f"Error calculating DMT for treated communes: {e}")
            return 0

    def _calculate_ctj_from_page3(self, collaborator, pd, datetime):
        """Calculate CTJ from page 3 (Traitement PA) column G for this collaborator."""
        try:
            # Check if we have page 3 data
            if 'Traitement PA' not in self.global_suivi_data:
                return 0

            df_pa = self.global_suivi_data['Traitement PA']
            if df_pa.empty:
                return 0

            # Filter data for this collaborator
            collab_pa_data = df_pa[df_pa.get('Collaborateur', '') == collaborator]
            if collab_pa_data.empty:
                return 0

            today = datetime.now().date()
            elements_today = 0

            # Check date column for today's work
            if 'Date traitement' in collab_pa_data.columns:
                for index, row in collab_pa_data.iterrows():
                    date_value = row.get('Date traitement', None)

                    if pd.isna(date_value) or date_value == '':
                        continue

                    try:
                        # Convert date to datetime if it's not already
                        if isinstance(date_value, str):
                            # Try different date formats
                            for date_format in ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y']:
                                try:
                                    date_obj = datetime.strptime(date_value, date_format).date()
                                    break
                                except ValueError:
                                    continue
                            else:
                                continue  # Skip if no format matches
                        else:
                            date_obj = pd.to_datetime(date_value).date()

                        # Check if date is today
                        if date_obj == today:
                            elements_today += 1

                    except Exception as e:
                        self.logger.debug(f"Error parsing date {date_value}: {e}")
                        continue

            return elements_today

        except Exception as e:
            self.logger.error(f"Error calculating CTJ from page 3: {e}")
            return 0

    def _calculate_team_kpis(self):
        """Calculate team-wide KPIs (DMT and CTJ) using corrected logic."""
        try:
            if not self.collaborator_stats:
                return

            total_dmt = 0
            total_ctj_today = 0
            collaborator_count_with_treated = 0

            # Calculate team totals
            for collaborator, stats in self.collaborator_stats.items():
                # For DMT: only count collaborators who have treated communes (DMT > 0)
                if stats['dmt'] > 0:
                    total_dmt += stats['dmt']
                    collaborator_count_with_treated += 1

                # For CTJ: sum all collaborators' daily capacity
                total_ctj_today += stats['ctj_today']

            # Calculate team DMT (average of collaborators with treated communes)
            if collaborator_count_with_treated > 0:
                self.team_statistics['team_dmt'] = total_dmt / collaborator_count_with_treated
            else:
                self.team_statistics['team_dmt'] = 0

            # Team CTJ is the sum of all individual CTJs
            self.team_statistics['team_ctj_today'] = total_ctj_today
            self.team_statistics['total_elements_today'] = total_ctj_today

            self.logger.info(f"Team KPIs calculated - DMT: {self.team_statistics['team_dmt']:.1f}min (from {collaborator_count_with_treated} collaborators with treated communes), CTJ Today: {total_ctj_today} elements")

        except Exception as e:
            self.logger.error(f"Error calculating team KPIs: {e}")

    def _analyze_commune_status_by_date(self, df_tickets, pd, datetime):
        """Analyze communes by status and delivery date for current month filtering."""
        try:
            current_month = datetime.now().month
            current_year = datetime.now().year

            # Debug: Show available columns
            self.logger.info(f"Available columns in Suivi Tickets: {list(df_tickets.columns)}")
            self.logger.info(f"DataFrame shape: {df_tickets.shape}")

            # Initialize counters
            communes_traitees_mois_courant = 0
            communes_autres_statuts = {
                'En Cours': 0,
                'En Attente': 0,
                'Rejeté': 0,
                'Autres': 0
            }

            # Check if required columns exist (handle column name variations)
            etat_column = None
            for col in df_tickets.columns:
                if 'etat ticket pa' in col.lower().strip():
                    etat_column = col
                    break

            has_etat_ticket_pa = etat_column is not None
            has_date_livraison = 'Date Livraison' in df_tickets.columns

            self.logger.info(f"Found status column: '{etat_column}' (exists: {has_etat_ticket_pa})")
            self.logger.info(f"Has 'Date Livraison' column: {has_date_livraison}")

            if not has_etat_ticket_pa:
                self.logger.warning("Column 'Etat Ticket PA' not found in Suivi Tickets sheet")
                # Try alternative column names
                alt_columns = [col for col in df_tickets.columns if 'etat' in col.lower() or 'statut' in col.lower()]
                self.logger.info(f"Alternative status columns found: {alt_columns}")
                return

            # Initialize counters
            current_month = datetime.now().month
            current_year = datetime.now().year
            communes_traitees_mois_courant = 0
            communes_autres_statuts = {}

            for index, row in df_tickets.iterrows():
                etat_ticket_pa = row.get(etat_column, '')
                date_livraison = row.get('Date Livraison', None)

                # Skip empty rows
                if pd.isna(etat_ticket_pa) or etat_ticket_pa == '':
                    continue

                # Check if commune is treated in current month
                if etat_ticket_pa == 'Traité' and has_date_livraison and not pd.isna(date_livraison):
                    try:
                        # Convert date to datetime if it's not already
                        if isinstance(date_livraison, str):
                            # Try different date formats
                            for date_format in ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y']:
                                try:
                                    date_obj = datetime.strptime(date_livraison, date_format)
                                    break
                                except ValueError:
                                    continue
                            else:
                                continue  # Skip if no format matches
                        else:
                            date_obj = pd.to_datetime(date_livraison)

                        # Check if date is in current month
                        if date_obj.month == current_month and date_obj.year == current_year:
                            communes_traitees_mois_courant += 1
                    except Exception as e:
                        self.logger.debug(f"Error parsing date {date_livraison}: {e}")
                        continue

                # Count other statuses (not treated)
                elif etat_ticket_pa.strip() != 'Traité':
                    status_clean = etat_ticket_pa.strip()
                    if status_clean in communes_autres_statuts:
                        communes_autres_statuts[status_clean] += 1
                    else:
                        communes_autres_statuts[status_clean] = 1

            # Store results
            self.team_statistics['communes_traitees_mois_courant'] = communes_traitees_mois_courant
            self.team_statistics['communes_autres_statuts'] = communes_autres_statuts

            self.logger.info(f"Communes traitées ce mois: {communes_traitees_mois_courant}")
            self.logger.info(f"Communes autres statuts: {communes_autres_statuts}")

        except Exception as e:
            self.logger.error(f"Error analyzing commune status by date: {e}")

    def _calculate_overall_averages(self):
        """Calculate overall team averages for comparison."""
        try:
            if not self.collaborator_stats:
                return

            total_cms_avg = 0
            total_pa_avg = 0
            total_finale_avg = 0
            collaborator_count = 0

            for collaborator, stats in self.collaborator_stats.items():
                if stats['commune_count'] > 0:  # Only count collaborators with communes
                    total_cms_avg += stats['avg_cms_duration']
                    total_pa_avg += stats['avg_pa_duration']
                    total_finale_avg += stats['avg_finale_duration']
                    collaborator_count += 1

            if collaborator_count > 0:
                self.overall_averages = {
                    'avg_cms_duration': total_cms_avg / collaborator_count,
                    'avg_pa_duration': total_pa_avg / collaborator_count,
                    'avg_finale_duration': total_finale_avg / collaborator_count,
                    'collaborator_count': collaborator_count
                }
            else:
                self.overall_averages = {
                    'avg_cms_duration': 0,
                    'avg_pa_duration': 0,
                    'avg_finale_duration': 0,
                    'collaborator_count': 0
                }

        except Exception as e:
            self.logger.error(f"Error calculating overall averages: {e}")
            self.overall_averages = {}

    def _update_overview_display(self):
        """Update the overview section with commune status statistics by current month."""
        try:
            # Clear existing content
            for widget in self.overview_content.winfo_children():
                widget.destroy()

            if not self.team_statistics:
                # Show debug message
                debug_label = tk.Label(
                    self.overview_content,
                    text="Aucune statistique disponible - Vérifiez que les données sont chargées",
                    font=UIConfig.FONT_SMALL,
                    fg=COLORS['DANGER'],
                    bg=COLORS['CARD']
                )
                debug_label.pack(anchor=tk.W)
                return

            # Debug: Show what we have in team_statistics
            self.logger.info(f"Team statistics keys: {list(self.team_statistics.keys())}")
            self.logger.info(f"Communes traitées mois courant: {self.team_statistics.get('communes_traitees_mois_courant', 'NOT_FOUND')}")
            self.logger.info(f"Communes autres statuts: {self.team_statistics.get('communes_autres_statuts', 'NOT_FOUND')}")

            # Current month communes section
            from datetime import datetime
            current_month_name = datetime.now().strftime("%B %Y")

            # Communes status section
            status_section = tk.Frame(self.overview_content, bg=COLORS['CARD'])
            status_section.pack(fill=tk.X, pady=(0, 10))

            status_title = tk.Label(
                status_section,
                text=f"📊 Statut des Communes - {current_month_name}",
                font=UIConfig.FONT_SUBTITLE,
                fg=COLORS['PRIMARY'],
                bg=COLORS['CARD']
            )
            status_title.pack(anchor=tk.W, pady=(0, 10))

            # Status list - vertical layout with specific order
            status_container = tk.Frame(status_section, bg=COLORS['CARD'])
            status_container.pack(fill=tk.X)

            # Define status order and icons
            status_order = ['Traité', 'En Cours', 'En Attente', 'Bloqué', 'Rejeté']
            status_icons = {
                'Traité': '✅',
                'En Cours': '🔄',
                'En Attente': '⏳',
                'Bloqué': '🚫',
                'Rejeté': '❌'
            }

            # Get all status data including treated communes
            traited_count = self.team_statistics.get('communes_traitees_mois_courant', 0)
            autres_statuts = self.team_statistics.get('communes_autres_statuts', {})

            # Combine all statuses
            all_statuses = {'Traité': traited_count}
            all_statuses.update(autres_statuts)

            # Display statuses in specified order
            for status in status_order:
                count = all_statuses.get(status, 0)
                icon = status_icons.get(status, '📋')
                self._create_status_item(status_container, f"{icon} {status}", str(count), status)



        except Exception as e:
            self.logger.error(f"Error updating overview display: {e}")

    def _create_status_item(self, parent, label, value, status_type):
        """Create a status item with consistent styling."""
        # Status colors
        status_colors = {
            'Traité': COLORS['SUCCESS'],
            'En Cours': COLORS['WARNING'],
            'En Attente': COLORS['INFO'],
            'Bloqué': COLORS['DANGER'],
            'Rejeté': COLORS['DANGER']
        }

        color = status_colors.get(status_type, COLORS['PRIMARY'])

        # Item frame
        item_frame = tk.Frame(parent, bg=COLORS['LIGHT'], relief='flat', bd=1)
        item_frame.pack(fill=tk.X, pady=2)

        # Content frame
        content_frame = tk.Frame(item_frame, bg=COLORS['LIGHT'])
        content_frame.pack(fill=tk.X, padx=12, pady=8)

        # Label (left side)
        label_widget = tk.Label(
            content_frame,
            text=label,
            font=UIConfig.FONT_SUBTITLE,
            fg=color,
            bg=COLORS['LIGHT']
        )
        label_widget.pack(side=tk.LEFT)

        # Value (right side)
        value_widget = tk.Label(
            content_frame,
            text=f"{value} communes",
            font=UIConfig.FONT_SUBTITLE,
            fg=color,
            bg=COLORS['LIGHT']
        )
        value_widget.pack(side=tk.RIGHT)

    def _create_stat_item(self, parent, row, col, label, value):
        """Create a statistics item in the overview grid."""
        item_frame = tk.Frame(parent, bg=COLORS['LIGHT'], relief='flat', bd=1)
        item_frame.grid(row=row, column=col, sticky="ew", padx=5, pady=5)

        # Configure grid weight
        parent.grid_rowconfigure(row, weight=1)

        # Content
        content = tk.Frame(item_frame, bg=COLORS['LIGHT'])
        content.pack(fill=tk.X, padx=10, pady=8)

        # Label
        label_widget = tk.Label(
            content,
            text=label,
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        label_widget.pack(anchor=tk.W)

        # Value
        value_widget = tk.Label(
            content,
            text=value,
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['PRIMARY'],
            bg=COLORS['LIGHT']
        )
        value_widget.pack(anchor=tk.W)

    def _update_statistics_display(self):
        """Update the detailed statistics display with overall averages and collaborator details."""
        try:
            # Clear existing content
            for widget in self.stats_display.winfo_children():
                widget.destroy()

            if not self.collaborator_stats:
                no_data_label = tk.Label(
                    self.stats_display,
                    text="Aucune donnée de collaborateur trouvée",
                    font=UIConfig.FONT_SMALL,
                    fg=COLORS['INFO'],
                    bg=COLORS['CARD']
                )
                no_data_label.pack(anchor=tk.W, pady=20)
                return

            # Overall Team Averages Section (at the top)
            if self.overall_averages:
                self._create_overall_averages_section()

                # Separator
                separator = tk.Frame(self.stats_display, bg=COLORS['BORDER'], height=1)
                separator.pack(fill=tk.X, pady=(10, 15))

            # Title for individual KPIs
            title_label = tk.Label(
                self.stats_display,
                text="📊 KPIs par Collaborateur - DMT & CTJ:",
                font=UIConfig.FONT_SUBTITLE,
                fg=COLORS['PRIMARY'],
                bg=COLORS['CARD']
            )
            title_label.pack(anchor=tk.W, pady=(0, 10))

            # Create collaborator cards
            for collaborator, stats in self.collaborator_stats.items():
                self._create_collaborator_card(collaborator, stats)

        except Exception as e:
            self.logger.error(f"Error updating statistics display: {e}")

    def _create_overall_averages_section(self):
        """Create the team KPIs section at the top."""
        # Team KPIs title
        kpi_title = tk.Label(
            self.stats_display,
            text="🎯 KPIs Équipe - Indicateurs de Performance",
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['PRIMARY'],
            bg=COLORS['CARD']
        )
        kpi_title.pack(anchor=tk.W, pady=(0, 8))

        # Team KPIs card
        kpi_card = tk.Frame(self.stats_display, bg=COLORS['SUCCESS'], relief='flat', bd=1)
        kpi_card.pack(fill=tk.X, pady=(0, 5))

        kpi_content = tk.Frame(kpi_card, bg=COLORS['SUCCESS'])
        kpi_content.pack(fill=tk.X, padx=12, pady=8)

        # KPIs grid
        kpi_grid = tk.Frame(kpi_content, bg=COLORS['SUCCESS'])
        kpi_grid.pack(fill=tk.X)

        kpi_grid.grid_columnconfigure(0, weight=1)
        kpi_grid.grid_columnconfigure(1, weight=1)
        kpi_grid.grid_columnconfigure(2, weight=1)

        # Team DMT (Average Treatment Duration)
        team_dmt = self._format_duration(self.team_statistics.get('team_dmt', 0))
        self._create_kpi_stat(kpi_grid, 0, 0, "⏱️ DMT Équipe", team_dmt, "Durée Moyenne de Traitement")

        # Team CTJ Today (Daily Treatment Capacity)
        team_ctj = self.team_statistics.get('team_ctj_today', 0)
        self._create_kpi_stat(kpi_grid, 0, 1, "📈 CTJ Aujourd'hui", f"{team_ctj} éléments", "Capacité de Traitement Journalier")

        # Total Collaborators
        total_collabs = len(self.collaborator_stats)
        self._create_kpi_stat(kpi_grid, 0, 2, "👥 Collaborateurs", f"{total_collabs} actifs", "Équipe en activité")

    def _create_kpi_stat(self, parent, row, col, label, value, description):
        """Create a KPI statistics item for the team section."""
        kpi_frame = tk.Frame(parent, bg='white', relief='flat', bd=1)
        kpi_frame.grid(row=row, column=col, sticky="ew", padx=3, pady=2)

        content = tk.Frame(kpi_frame, bg='white')
        content.pack(fill=tk.X, padx=6, pady=4)

        # Label
        label_widget = tk.Label(
            content,
            text=label,
            font=("Segoe UI", 8, "bold"),
            fg=COLORS['SUCCESS'],
            bg='white'
        )
        label_widget.pack(anchor=tk.W)

        # Value
        value_widget = tk.Label(
            content,
            text=value,
            font=("Segoe UI", 10, "bold"),
            fg=COLORS['PRIMARY'],
            bg='white'
        )
        value_widget.pack(anchor=tk.W)

        # Description
        desc_widget = tk.Label(
            content,
            text=description,
            font=("Segoe UI", 6),
            fg=COLORS['INFO'],
            bg='white'
        )
        desc_widget.pack(anchor=tk.W)

    def _create_avg_stat(self, parent, row, col, label, value):
        """Create an average statistics item for the overall section."""
        avg_frame = tk.Frame(parent, bg='white', relief='flat', bd=1)
        avg_frame.grid(row=row, column=col, sticky="ew", padx=3, pady=2)

        content = tk.Frame(avg_frame, bg='white')
        content.pack(fill=tk.X, padx=6, pady=4)

        # Label
        label_widget = tk.Label(
            content,
            text=label,
            font=("Segoe UI", 7, "bold"),
            fg=COLORS['SECONDARY'],
            bg='white'
        )
        label_widget.pack(anchor=tk.W)

        # Value
        value_widget = tk.Label(
            content,
            text=value,
            font=UIConfig.FONT_SMALL,
            fg=COLORS['PRIMARY'],
            bg='white'
        )
        value_widget.pack(anchor=tk.W)

    def _create_collaborator_card(self, collaborator, stats):
        """Create a card displaying KPI-focused statistics for a collaborator."""
        # Card frame
        card_frame = tk.Frame(self.stats_display, bg=COLORS['LIGHT'], relief='flat', bd=1)
        card_frame.pack(fill=tk.X, pady=(0, 8))

        # Card content
        card_content = tk.Frame(card_frame, bg=COLORS['LIGHT'])
        card_content.pack(fill=tk.X, padx=12, pady=8)

        # Collaborator name and commune count
        header_frame = tk.Frame(card_content, bg=COLORS['LIGHT'])
        header_frame.pack(fill=tk.X, pady=(0, 6))

        name_label = tk.Label(
            header_frame,
            text=f"👤 {collaborator}",
            font=UIConfig.FONT_TITLE,
            fg=COLORS['PRIMARY'],
            bg=COLORS['LIGHT']
        )
        name_label.pack(side=tk.LEFT)

        # Communes count prominently displayed
        communes_label = tk.Label(
            header_frame,
            text=f"🏘️ {stats['commune_count']} communes",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['INFO'],
            bg=COLORS['LIGHT']
        )
        communes_label.pack(side=tk.RIGHT)

        # KPI Statistics grid - focused only on essential KPIs
        kpi_grid = tk.Frame(card_content, bg=COLORS['LIGHT'])
        kpi_grid.pack(fill=tk.X)

        # Configure grid for 1 row x 2 columns (only DMT and CTJ)
        kpi_grid.grid_columnconfigure(0, weight=1)
        kpi_grid.grid_columnconfigure(1, weight=1)

        # Essential KPIs only - DMT and CTJ
        dmt_value = self._format_duration(stats.get('dmt', 0))
        ctj_value = f"{stats.get('ctj_today', 0)} éléments"

        self._create_mini_stat(kpi_grid, 0, 0, "⏱️ DMT", dmt_value, highlight=True)
        self._create_mini_stat(kpi_grid, 0, 1, "📈 CTJ Aujourd'hui", ctj_value, highlight=True)

    def _create_mini_stat(self, parent, row, col, label, value, highlight=False):
        """Create a mini statistics item with optional highlighting."""
        bg_color = COLORS['PRIMARY_LIGHT'] if highlight else COLORS['WHITE']
        text_color = 'white' if highlight else COLORS['PRIMARY']
        label_color = 'white' if highlight else COLORS['INFO']

        mini_frame = tk.Frame(parent, bg=bg_color, relief='flat', bd=1)
        mini_frame.grid(row=row, column=col, sticky="ew", padx=2, pady=2)

        # Content
        content = tk.Frame(mini_frame, bg=bg_color)
        content.pack(fill=tk.X, padx=4, pady=3)

        # Label
        label_widget = tk.Label(
            content,
            text=label,
            font=("Segoe UI", 6, "bold" if highlight else "normal"),
            fg=label_color,
            bg=bg_color
        )
        label_widget.pack(anchor=tk.W)

        # Value
        value_widget = tk.Label(
            content,
            text=value,
            font=("Segoe UI", 8, "bold" if highlight else "normal"),
            fg=text_color,
            bg=bg_color
        )
        value_widget.pack(anchor=tk.W)

    def _format_duration(self, minutes):
        """Format duration in minutes only."""
        if minutes == 0:
            return "0 min"

        # Always display in minutes only
        return f"{int(minutes)} min"

    def _refresh_statistics(self):
        """Refresh the statistics by reloading data."""
        self._load_global_data()

    def _load_shortcut(self, event=None):
        """Keyboard shortcut for loading data."""
        self._load_global_data()

    def _refresh_shortcut(self, event=None):
        """Keyboard shortcut for refreshing."""
        if self.global_suivi_data:
            self._refresh_statistics()

    def _restore_session(self):
        """Restore session data if available."""
        try:
            if self.session_manager:
                # Auto-load data if available
                pass
        except Exception as e:
            self.logger.error(f"Error restoring session: {e}")

    def cleanup(self):
        """Clean up resources when module is destroyed."""
        try:
            if self.auto_save_manager and self.navigation_manager and hasattr(self.navigation_manager, 'root'):
                self.auto_save_manager.stop_auto_save(self.navigation_manager.root)

            self.logger.info("Team Stats module cleaned up")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def _export_ctj_to_excel(self):
        """Export CTJ statistics to Excel format with filters."""
        try:
            # Verify dependencies first
            try:
                from utils.lazy_imports import get_pandas
                pd = get_pandas()
                import openpyxl
                self.logger.info("Dependencies verified successfully")
            except ImportError as ie:
                messagebox.showerror("Dépendance manquante", f"Module requis non trouvé: {ie}\nVeuillez installer les dépendances nécessaires.")
                return
            except Exception as de:
                messagebox.showerror("Erreur de dépendance", f"Erreur lors de la vérification des dépendances: {de}")
                return

            # Get filter values
            selected_collaborator = self.collab_var.get()
            selected_month = self.month_var.get()
            selected_year = self.year_var.get()

            if not selected_collaborator or not selected_month or not selected_year:
                messagebox.showwarning("Filtres requis", "Veuillez sélectionner un collaborateur, un mois et une année.")
                return

            self.logger.info(f"Starting CTJ export for {selected_collaborator}, {selected_month} {selected_year}")

            # Check if we have the required data first
            if not hasattr(self, 'global_suivi_data') or not self.global_suivi_data:
                messagebox.showerror("Aucune donnée", "Aucune donnée globale chargée. Veuillez d'abord charger les données avec le module 'Suivi Global Tickets'.")
                return

            if 'Traitement PA' not in self.global_suivi_data:
                available_sheets = list(self.global_suivi_data.keys()) if self.global_suivi_data else []
                messagebox.showerror("Données manquantes", f"Les données 'Traitement PA' ne sont pas disponibles.\n\nFeuilles disponibles: {available_sheets}\n\nVeuillez vérifier que le fichier global contient la feuille 'Traitement PA'.")
                return

            # Calculate CTJ data for the selected filters
            ctj_data = self._calculate_monthly_ctj(selected_collaborator, selected_month, selected_year)

            if not ctj_data:
                # Provide more specific error message for team vs individual
                if selected_collaborator == "Toute l'équipe":
                    messagebox.showinfo("Aucune donnée", f"Aucune donnée CTJ trouvée pour l'équipe en {selected_month} {selected_year}.\n\nVérifiez que :\n- Les données 'Traitement PA' contiennent des collaborateurs\n- Les dates de traitement correspondent à la période sélectionnée")
                else:
                    messagebox.showinfo("Aucune donnée", f"Aucune donnée CTJ trouvée pour {selected_collaborator} en {selected_month} {selected_year}.\n\nVérifiez que :\n- Le collaborateur existe dans les données\n- Les dates de traitement correspondent à la période sélectionnée")
                return

            self.logger.info(f"CTJ data calculated: {len(ctj_data)} records")

            # Validate data structure
            if not isinstance(ctj_data, list) or len(ctj_data) == 0:
                messagebox.showerror("Erreur de données", "Les données CTJ ne sont pas dans le format attendu.")
                return

            # Create Excel file
            self._create_ctj_excel_file(ctj_data, selected_collaborator, selected_month, selected_year)

        except Exception as e:
            self.logger.error(f"Error exporting CTJ to Excel: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            messagebox.showerror("Erreur", f"Erreur lors de l'export CTJ Excel:\n{str(e)}")

    def _calculate_monthly_ctj(self, collaborator, month_name, year):
        """Calculate CTJ data for a specific collaborator and month."""
        try:
            from datetime import datetime
            import calendar
            from utils.lazy_imports import get_pandas

            # Convert month name to number
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)

            # Get page 3 data (Traitement PA)
            if 'Traitement PA' not in self.global_suivi_data:
                self.logger.warning("No 'Traitement PA' data found in global suivi data")
                return []

            df_pa = self.global_suivi_data['Traitement PA']
            if df_pa.empty:
                self.logger.warning("'Traitement PA' data is empty")
                return []

            # Check for required columns
            required_columns = ['Date traitement', 'Collaborateur']
            missing_columns = [col for col in required_columns if col not in df_pa.columns]
            if missing_columns:
                self.logger.warning(f"Missing required columns in PA data: {missing_columns}")
                self.logger.info(f"Available columns: {list(df_pa.columns)}")
                return []

            # Filter data for this collaborator (or all if "Toute l'équipe")
            if collaborator == "Toute l'équipe":
                collab_data = df_pa
                self.logger.info(f"Team export: Total PA data rows: {len(collab_data)}")
            else:
                collab_data = df_pa[df_pa.get('Collaborateur', '') == collaborator]
                if collab_data.empty:
                    self.logger.warning(f"No data found for collaborator: {collaborator}")
                    return []
                self.logger.info(f"Individual export: Found {len(collab_data)} rows for {collaborator}")

            pd = get_pandas()
            # Get number of days in the month
            days_in_month = calendar.monthrange(year_num, month_num)[1]

            if collaborator == "Toute l'équipe":
                # For team export, create data by collaborator and day
                team_ctj = {}

                # Check if 'Collaborateur' column exists
                if 'Collaborateur' not in collab_data.columns:
                    self.logger.warning("No 'Collaborateur' column found in PA data")
                    return []

                collaborators_in_data = collab_data['Collaborateur'].unique()
                valid_collaborators = [str(collab).strip() for collab in collaborators_in_data
                                     if pd.notna(collab) and str(collab).strip()]

                self.logger.info(f"Team export: Found collaborators: {valid_collaborators}")

                if not valid_collaborators:
                    self.logger.warning("No valid collaborators found in PA data")
                    return []

                # Initialize structure for all collaborators and days
                for collab in valid_collaborators:
                    team_ctj[collab] = {}
                    for day in range(1, days_in_month + 1):
                        team_ctj[collab][day] = 0

                # Count elements processed each day by each collaborator
                processed_rows = 0
                valid_dates = 0

                for index, row in collab_data.iterrows():
                    date_value = row.get('Date traitement', None)
                    collab_name = row.get('Collaborateur', '')

                    if pd.isna(date_value) or date_value == '' or pd.isna(collab_name) or str(collab_name).strip() == '':
                        continue

                    collab_name = str(collab_name).strip()
                    if collab_name not in team_ctj:
                        continue

                    processed_rows += 1

                    try:
                        # Convert date to datetime
                        if isinstance(date_value, str):
                            for date_format in ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y']:
                                try:
                                    date_obj = datetime.strptime(date_value, date_format)
                                    break
                                except ValueError:
                                    continue
                            else:
                                continue
                        else:
                            date_obj = pd.to_datetime(date_value)

                        # Check if date is in the selected month/year
                        if date_obj.month == month_num and date_obj.year == year_num:
                            day = date_obj.day
                            team_ctj[collab_name][day] += 1
                            valid_dates += 1

                    except Exception as e:
                        self.logger.debug(f"Error parsing date {date_value}: {e}")
                        continue

                self.logger.info(f"Team export: Processed {processed_rows} rows, found {valid_dates} valid dates for {month_name} {year}")

                # Convert to list format for Excel export (team format)
                ctj_data = []
                total_team_ctj = 0

                for day in range(1, days_in_month + 1):
                    date_str = f"{day:02d}/{month_num:02d}/{year_num}"
                    row_data = {'Date': date_str}

                    # Add each collaborator's CTJ for this day
                    total_day = 0
                    for collab in sorted(team_ctj.keys()):
                        ctj_value = team_ctj[collab][day]
                        row_data[collab] = ctj_value
                        total_day += ctj_value

                    row_data['Total'] = total_day
                    total_team_ctj += total_day
                    ctj_data.append(row_data)

                self.logger.info(f"Team export: Generated {len(ctj_data)} days of data, total CTJ: {total_team_ctj}")

                # If no data was found, create a minimal structure for the export to work
                if total_team_ctj == 0 and not team_ctj:
                    self.logger.warning("No CTJ data found for team export, creating minimal structure")
                    # Create a minimal structure with at least one dummy collaborator
                    for day in range(1, days_in_month + 1):
                        date_str = f"{day:02d}/{month_num:02d}/{year_num}"
                        ctj_data[day-1] = {'Date': date_str, 'Aucune donnée': 0, 'Total': 0}

                return ctj_data

            else:
                # Individual collaborator export
                daily_ctj = {}

                # Initialize all days with 0
                for day in range(1, days_in_month + 1):
                    daily_ctj[day] = 0

                # Count elements processed each day
                for index, row in collab_data.iterrows():
                    date_value = row.get('Date traitement', None)

                    if pd.isna(date_value) or date_value == '':
                        continue

                    try:
                        # Convert date to datetime
                        if isinstance(date_value, str):
                            for date_format in ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y']:
                                try:
                                    date_obj = datetime.strptime(date_value, date_format)
                                    break
                                except ValueError:
                                    continue
                            else:
                                continue
                        else:
                            date_obj = pd.to_datetime(date_value)

                        # Check if date is in the selected month/year
                        if date_obj.month == month_num and date_obj.year == year_num:
                            day = date_obj.day
                            daily_ctj[day] += 1

                    except Exception as e:
                        self.logger.debug(f"Error parsing date {date_value}: {e}")
                        continue

                # Convert to list format for Excel export
                ctj_data = []
                for day in range(1, days_in_month + 1):
                    date_str = f"{day:02d}/{month_num:02d}/{year_num}"
                    ctj_data.append({
                        'Date': date_str,
                        'CTJ': daily_ctj[day]
                    })

                return ctj_data

        except Exception as e:
            self.logger.error(f"Error calculating monthly CTJ: {e}")
            return []

    def _create_ctj_excel_file(self, ctj_data, collaborator, month_name, year):
        """Create Excel file with CTJ data in horizontal date format."""
        try:
            import os
            from datetime import datetime, timedelta
            from tkinter import filedialog
            import calendar

            # Generate filename
            current_date = datetime.now().strftime("%Y%m%d")
            safe_collaborator = collaborator.replace(' ', '_').replace("'", "")
            filename = f"Stat_{safe_collaborator}_{current_date}.xlsx"

            # Ask user for save location
            file_path = filedialog.asksaveasfilename(
                title="Enregistrer les statistiques CTJ",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=filename
            )

            if not file_path:
                return

            # Create the new format Excel file
            self._create_horizontal_ctj_excel(file_path, ctj_data, collaborator, month_name, year)

            messagebox.showinfo("Export réussi", f"Fichier CTJ exporté avec succès:\n{file_path}")
            self.logger.info(f"CTJ Excel file created: {file_path}")

        except Exception as e:
            self.logger.error(f"Error creating CTJ Excel file: {e}")

            # Provide user-friendly error messages
            error_str = str(e)
            if "Permission denied" in error_str or "WinError 33" in error_str or "annulé par l'utilisateur" in error_str:
                # File access issues - user already saw the dialog or cancelled
                if "annulé par l'utilisateur" not in error_str:
                    messagebox.showwarning(
                        "Fichier en cours d'utilisation",
                        "🔒 Le fichier d'export est actuellement ouvert dans Excel.\n\n"
                        "Veuillez fermer Excel et réessayer l'export."
                    )
            else:
                # Other errors
                messagebox.showerror("Erreur Export", f"Erreur lors de la création du fichier Excel:\n{str(e)}")
            raise

    def _create_horizontal_ctj_excel(self, file_path, ctj_data, collaborator, month_name, year):
        """Create streamlined Excel file with only CTJ Quotidien sheet."""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from datetime import datetime, timedelta
            import calendar

            # Create workbook
            workbook = openpyxl.Workbook()

            # Get the default sheet to remove it later
            default_sheet = workbook.active

            # Create only the CTJ Quotidien sheet as requested
            success_daily = self._create_streamlined_daily_sheet(workbook, ctj_data, collaborator, month_name, year)

            # Always remove the default sheet if we successfully created our custom sheet
            if success_daily:
                try:
                    workbook.remove(default_sheet)
                    self.logger.info("Default sheet removed successfully")
                except Exception as e:
                    self.logger.warning(f"Could not remove default sheet: {e}")
            else:
                # If creation failed, provide detailed error message
                error_msg = f"Failed to create CTJ export sheet for {collaborator}. Please check if data is available for the selected period."
                self.logger.error(error_msg)
                raise Exception(error_msg)

            # Check file access before saving
            if os.path.exists(file_path):
                access_result = check_file_access(file_path, 'w')
                if not access_result['accessible']:
                    self.logger.warning(f"File access issue during export: {access_result['error_message']}")

                    # Show user-friendly dialog and get retry decision
                    if access_result['error_type'] in ['file_locked', 'permission_denied']:
                        # This is a file-in-use situation, show the custom dialog
                        retry = self._show_file_access_dialog(access_result, file_path)
                        if not retry:
                            raise Exception("Export annulé par l'utilisateur")

                        # User wants to retry, check again
                        retry_access = check_file_access(file_path, 'w')
                        if not retry_access['accessible']:
                            # Still not accessible, raise with user-friendly message
                            raise Exception(f"{retry_access['user_message']}\n\nVeuillez fermer Excel et réessayer l'export.")
                    else:
                        # Other types of errors, raise with user message
                        raise Exception(access_result['user_message'])

            # Save the streamlined workbook
            workbook.save(file_path)

            self.logger.info(f"Streamlined CTJ Excel file created successfully: {file_path}")

        except Exception as e:
            self.logger.error(f"Error creating streamlined CTJ Excel: {e}")
            raise




    def _apply_horizontal_formatting(self, worksheet, num_days, month_num, year, num_collaborators):
        """Apply formatting to horizontal CTJ Excel layout."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from datetime import datetime
            import calendar

            # Define colors
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            weekend_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")  # Gray
            zero_ctj_fill = PatternFill(start_color="FFC0CB", end_color="FFC0CB", fill_type="solid")  # Pink

            # Define fonts
            header_font = Font(bold=True, color="FFFFFF")
            bold_font = Font(bold=True)

            # Define alignment
            center_alignment = Alignment(horizontal="center", vertical="center")

            # Define borders
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Format header row (row 1 - collaborator name)
            worksheet.cell(row=1, column=1).font = bold_font
            worksheet.cell(row=1, column=1).alignment = center_alignment

            # Format second row (row 2 - "Eléments Traités" and dates)
            worksheet.cell(row=2, column=1, value="Eléments Traités")
            worksheet.cell(row=2, column=1).font = header_font
            worksheet.cell(row=2, column=1).fill = header_fill
            worksheet.cell(row=2, column=1).alignment = center_alignment
            worksheet.cell(row=2, column=1).border = thin_border

            # Format date headers and apply weekend/zero formatting
            for day in range(1, num_days + 1):
                col = day + 1
                date_obj = datetime(year, month_num, day)

                # Header cell formatting
                header_cell = worksheet.cell(row=2, column=col)
                header_cell.font = header_font
                header_cell.fill = header_fill
                header_cell.alignment = center_alignment
                header_cell.border = thin_border

                # Check if weekend (Saturday=5, Sunday=6)
                is_weekend = date_obj.weekday() >= 5

                # Format data cells for this day
                for row in range(3, 3 + num_collaborators):
                    data_cell = worksheet.cell(row=row, column=col)
                    data_cell.alignment = center_alignment
                    data_cell.border = thin_border

                    # Apply weekend formatting
                    if is_weekend:
                        data_cell.fill = weekend_fill
                    # Apply zero CTJ formatting (pink)
                    elif data_cell.value == 0:
                        data_cell.fill = zero_ctj_fill

            # Format collaborator names column
            for row in range(3, 3 + num_collaborators):
                name_cell = worksheet.cell(row=row, column=1)
                name_cell.font = bold_font
                name_cell.alignment = Alignment(horizontal="left", vertical="center")
                name_cell.border = thin_border

            # Auto-adjust column widths
            # Column A (collaborator names) - wider
            worksheet.column_dimensions['A'].width = 20

            # Date columns - narrower
            for day in range(1, num_days + 1):
                col_letter = worksheet.cell(row=2, column=day + 1).column_letter
                worksheet.column_dimensions[col_letter].width = 4

            # Add total row if multiple collaborators
            if num_collaborators > 1:
                total_row = 3 + num_collaborators
                worksheet.cell(row=total_row, column=1, value="TOTAL")
                worksheet.cell(row=total_row, column=1).font = bold_font
                worksheet.cell(row=total_row, column=1).fill = header_fill
                worksheet.cell(row=total_row, column=1).alignment = center_alignment
                worksheet.cell(row=total_row, column=1).border = thin_border

                # Calculate totals for each day
                for day in range(1, num_days + 1):
                    col = day + 1
                    date_obj = datetime(year, month_num, day)
                    is_weekend = date_obj.weekday() >= 5

                    # Sum values from all collaborators for this day
                    total_value = 0
                    for row in range(3, 3 + num_collaborators):
                        cell_value = worksheet.cell(row=row, column=col).value
                        if isinstance(cell_value, (int, float)):
                            total_value += cell_value

                    total_cell = worksheet.cell(row=total_row, column=col)
                    total_cell.value = total_value
                    total_cell.font = bold_font
                    total_cell.alignment = center_alignment
                    total_cell.border = thin_border

                    # Apply formatting
                    if is_weekend:
                        total_cell.fill = weekend_fill
                    elif total_value == 0:
                        total_cell.fill = zero_ctj_fill

        except Exception as e:
            self.logger.warning(f"Could not apply Excel formatting: {e}")
            # Continue without formatting if there's an error







    def _create_streamlined_daily_sheet(self, workbook, ctj_data, collaborator, month_name, year):
        """Create streamlined daily CTJ sheet with only essential data."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from datetime import datetime
            import calendar

            # Create streamlined daily sheet
            daily_sheet = workbook.create_sheet(title="CTJ Quotidien")

            # Define styles
            header_font = Font(size=11, bold=True, color="FFFFFF")
            data_font = Font(size=10)
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            weekend_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
            zero_fill = PatternFill(start_color="FFC0CB", end_color="FFC0CB", fill_type="solid")

            center_align = Alignment(horizontal="center", vertical="center")

            # Get month info
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)
            days_in_month = calendar.monthrange(year_num, month_num)[1]

            # Create date headers with day and abbreviated month format
            month_abbrev = {
                1: "Jan", 2: "Fév", 3: "Mar", 4: "Avr", 5: "Mai", 6: "Jun",
                7: "Jul", 8: "Aoû", 9: "Sep", 10: "Oct", 11: "Nov", 12: "Déc"
            }

            if collaborator == "Toute l'équipe":
                # Team export: stack individual collaborators vertically
                return self._create_team_stacked_export(daily_sheet, ctj_data, month_name, year,
                                                      header_font, data_font, header_fill,
                                                      weekend_fill, zero_fill, center_align)
            else:
                # Individual collaborator export
                return self._create_individual_export(daily_sheet, ctj_data, collaborator, month_name, year,
                                                    header_font, data_font, header_fill,
                                                    weekend_fill, zero_fill, center_align)

        except Exception as e:
            self.logger.error(f"Error creating streamlined daily sheet: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    def _create_individual_export(self, daily_sheet, ctj_data, collaborator, month_name, year,
                                header_font, data_font, header_fill, weekend_fill, zero_fill, center_align):
        """Create individual collaborator export with only CTJ data."""
        try:
            from datetime import datetime
            import calendar
            from openpyxl.styles import Font, Border, Side, PatternFill

            self.logger.info(f"Creating individual export for {collaborator}, data length: {len(ctj_data) if ctj_data else 0}")

            # Define border style
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Get month info
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)
            days_in_month = calendar.monthrange(year_num, month_num)[1]

            # Create date headers with day and abbreviated month format
            month_abbrev = {
                1: "Jan", 2: "Fév", 3: "Mar", 4: "Avr", 5: "Mai", 6: "Jun",
                7: "Jul", 8: "Aoû", 9: "Sep", 10: "Oct", 11: "Nov", 12: "Déc"
            }

            # Title
            daily_sheet.merge_cells('A1:AH1')
            title_cell = daily_sheet['A1']
            title_cell.value = f"CTJ QUOTIDIEN - {collaborator.upper()} - {month_name} {year}"
            title_cell.font = Font(size=14, bold=True, color="FFFFFF")
            title_cell.fill = header_fill
            title_cell.alignment = center_align

            # Headers with new date format
            headers = ['Collaborateur']
            for day in range(1, days_in_month + 1):
                headers.append(f"{day} {month_abbrev[month_num]}")

            # Write headers with borders
            for col, header in enumerate(headers, 1):
                cell = daily_sheet.cell(row=3, column=col)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_align
                cell.border = thin_border

            # Write collaborator name with border
            daily_sheet.cell(row=4, column=1).value = collaborator
            daily_sheet.cell(row=4, column=1).font = Font(bold=True)
            daily_sheet.cell(row=4, column=1).border = thin_border

            # Write CTJ data and calculate cumulative
            total_ctj_written = 0
            cumulative_ctj = 0
            daily_values = []

            for day in range(1, days_in_month + 1):
                col = day + 1
                ctj_value = 0

                if day <= len(ctj_data):
                    row_data = ctj_data[day - 1]
                    ctj_value = row_data.get('CTJ', 0)
                    if ctj_value > 0:
                        total_ctj_written += ctj_value

                # Store daily value for calculations
                daily_values.append(ctj_value)
                cumulative_ctj += ctj_value

                cell = daily_sheet.cell(row=4, column=col)
                cell.value = ctj_value if ctj_value > 0 else ""
                cell.font = data_font
                cell.alignment = center_align
                cell.border = thin_border

                # Apply conditional formatting
                date_obj = datetime(year_num, month_num, day)
                is_weekend = date_obj.weekday() >= 5

                if is_weekend:
                    cell.fill = weekend_fill
                elif ctj_value == 0:
                    cell.fill = zero_fill

            # Add cumulative CTJ row
            daily_sheet.cell(row=5, column=1).value = "Cumulé CTJ"
            daily_sheet.cell(row=5, column=1).font = Font(bold=True, italic=True)
            daily_sheet.cell(row=5, column=1).border = thin_border

            running_total = 0
            for day in range(1, days_in_month + 1):
                col = day + 1
                running_total += daily_values[day - 1]

                cell = daily_sheet.cell(row=5, column=col)
                cell.value = running_total if running_total > 0 else ""
                cell.font = Font(size=9, italic=True)
                cell.alignment = center_align
                cell.border = thin_border
                cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")  # Light blue

            # Add global summary section
            self._add_individual_summary(daily_sheet, daily_values, collaborator, month_name, year,
                                       days_in_month, thin_border, header_font, data_font, center_align)

            self.logger.info(f"Individual export: wrote {total_ctj_written} total CTJ values for {collaborator}")

            # Auto-fit all columns based on content (now checking more rows including summary)
            for col in range(1, days_in_month + 2):
                # Get column letter safely
                from openpyxl.utils import get_column_letter
                column_letter = get_column_letter(col)

                # Calculate the maximum width needed for this column
                max_width = 0
                # Check more rows now that we have summary section
                for row in range(2, daily_sheet.max_row + 1):  # Check all rows with content
                    cell = daily_sheet.cell(row=row, column=col)
                    if cell.value and not hasattr(cell, 'coordinate'):  # Skip merged cells
                        cell_width = len(str(cell.value)) + 2  # Add padding
                        max_width = max(max_width, cell_width)

                # Set minimum and maximum widths
                width = max(8, min(max_width, 25))  # Min 8, Max 25
                daily_sheet.column_dimensions[column_letter].width = width

            # Make first column wider for collaborator name and labels
            daily_sheet.column_dimensions['A'].width = max(25, daily_sheet.column_dimensions['A'].width)

            self.logger.info("Individual export sheet created successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error creating individual export: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    def _add_individual_summary(self, daily_sheet, daily_values, collaborator, month_name, year,
                              days_in_month, thin_border, header_font, data_font, center_align):
        """Add global summary section for individual collaborator."""
        try:
            from openpyxl.styles import Font, PatternFill

            # Calculate statistics
            total_ctj = sum(daily_values)
            working_days = len([v for v in daily_values if v > 0])
            avg_ctj_per_working_day = total_ctj / working_days if working_days > 0 else 0
            avg_ctj_per_month_day = total_ctj / days_in_month if days_in_month > 0 else 0
            max_daily_ctj = max(daily_values) if daily_values else 0

            # Calculate working days in month (excluding weekends)
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)
            working_days_in_month = self._calculate_working_days_in_month(month_num, year_num)

            # Summary section styling
            summary_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")  # Light blue
            label_font = Font(size=10, bold=True)
            value_font = Font(size=10)

            # Start summary section 2 rows below the cumulative row
            start_row = 8

            # Title for summary section
            daily_sheet.merge_cells(f'A{start_row}:D{start_row}')
            title_cell = daily_sheet[f'A{start_row}']
            title_cell.value = f"📊 RÉSUMÉ GLOBAL - {month_name} {year}"
            title_cell.font = Font(size=12, bold=True, color="1F4E79")
            title_cell.alignment = center_align
            title_cell.fill = summary_fill
            title_cell.border = thin_border

            # Summary data
            summary_data = [
                ("CTJ Total du Mois", f"{total_ctj:,}"),
                ("Jours Travaillés", f"{working_days}"),
                ("CTJ Moyen/Jour Travaillé", f"{avg_ctj_per_working_day:.1f}"),
                ("CTJ Moyen/Jour du Mois", f"{avg_ctj_per_month_day:.1f}"),
                ("CTJ Maximum en 1 Jour", f"{max_daily_ctj}"),
                ("Taux d'Activité", f"{(working_days/working_days_in_month)*100:.1f}%")
            ]

            # Write summary data
            for i, (label, value) in enumerate(summary_data):
                row = start_row + 1 + i

                # Label column
                label_cell = daily_sheet.cell(row=row, column=1)
                label_cell.value = label
                label_cell.font = label_font
                label_cell.border = thin_border
                label_cell.fill = summary_fill

                # Value column
                value_cell = daily_sheet.cell(row=row, column=2)
                value_cell.value = value
                value_cell.font = value_font
                value_cell.alignment = center_align
                value_cell.border = thin_border

                # Empty cells for formatting consistency
                for col in range(3, 5):
                    empty_cell = daily_sheet.cell(row=row, column=col)
                    empty_cell.border = thin_border
                    empty_cell.fill = summary_fill

            self.logger.info(f"Added individual summary for {collaborator}: Total CTJ={total_ctj}, Working days={working_days}")

        except Exception as e:
            self.logger.error(f"Error adding individual summary: {e}")

    def _create_team_stacked_export(self, daily_sheet, ctj_data, month_name, year,
                                  header_font, data_font, header_fill, weekend_fill, zero_fill, center_align):
        """Create team export with individual collaborators stacked vertically."""
        try:
            from datetime import datetime
            import calendar
            from openpyxl.styles import Font, Border, Side, PatternFill, Alignment

            self.logger.info(f"Creating team export, data length: {len(ctj_data) if ctj_data else 0}")

            # Define border style
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Get month info
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)
            days_in_month = calendar.monthrange(year_num, month_num)[1]

            # Create date headers with day and abbreviated month format
            month_abbrev = {
                1: "Jan", 2: "Fév", 3: "Mar", 4: "Avr", 5: "Mai", 6: "Jun",
                7: "Jul", 8: "Aoû", 9: "Sep", 10: "Oct", 11: "Nov", 12: "Déc"
            }

            # Title
            daily_sheet.merge_cells('A1:AH1')
            title_cell = daily_sheet['A1']
            title_cell.value = f"CTJ QUOTIDIEN - TOUTE L'ÉQUIPE - {month_name} {year}"
            title_cell.font = Font(size=14, bold=True, color="FFFFFF")
            title_cell.fill = header_fill
            title_cell.alignment = center_align

            # Headers with new date format
            headers = ['Collaborateur']
            for day in range(1, days_in_month + 1):
                headers.append(f"{day} {month_abbrev[month_num]}")

            # Write headers with borders
            for col, header in enumerate(headers, 1):
                cell = daily_sheet.cell(row=3, column=col)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_align
                cell.border = thin_border

            # Initialize current_row for auto-fitting
            current_row = 4

            # Extract collaborators from ctj_data and stack them vertically
            if ctj_data and len(ctj_data) > 0:
                # Get all collaborators from the first day's data (excluding 'Date' and 'Total')
                first_day_data = ctj_data[0]
                collaborators = [key for key in first_day_data.keys() if key not in ['Date', 'Total']]

                self.logger.info(f"Team export: found {len(collaborators)} collaborators: {collaborators}")

                # Check if we have any real collaborators (not just 'Aucune donnée')
                real_collaborators = [c for c in collaborators if c != 'Aucune donnée']
                if not real_collaborators and 'Aucune donnée' in collaborators:
                    self.logger.warning("Only dummy data found for team export")

                team_daily_values = {}  # Store daily values for each collaborator

                for collaborator in sorted(collaborators):
                    # Write collaborator name with border
                    daily_sheet.cell(row=current_row, column=1).value = collaborator
                    daily_sheet.cell(row=current_row, column=1).font = Font(bold=True)
                    daily_sheet.cell(row=current_row, column=1).border = thin_border

                    # Write CTJ data for this collaborator
                    total_ctj_for_collab = 0
                    daily_values = []

                    for day in range(1, days_in_month + 1):
                        col = day + 1
                        ctj_value = 0

                        if day <= len(ctj_data):
                            row_data = ctj_data[day - 1]
                            ctj_value = row_data.get(collaborator, 0)
                            if ctj_value > 0:
                                total_ctj_for_collab += ctj_value

                        daily_values.append(ctj_value)

                        cell = daily_sheet.cell(row=current_row, column=col)
                        cell.value = ctj_value if ctj_value > 0 else ""
                        cell.font = data_font
                        cell.alignment = center_align
                        cell.border = thin_border

                        # Apply conditional formatting
                        date_obj = datetime(year_num, month_num, day)
                        is_weekend = date_obj.weekday() >= 5

                        if is_weekend:
                            cell.fill = weekend_fill
                        elif ctj_value == 0:
                            cell.fill = zero_fill

                    # Store daily values for team summary
                    team_daily_values[collaborator] = daily_values

                    self.logger.info(f"Team export: wrote {total_ctj_for_collab} total CTJ values for {collaborator}")
                    current_row += 1

                    # Add cumulative row for this collaborator
                    daily_sheet.cell(row=current_row, column=1).value = f"Cumulé {collaborator}"
                    daily_sheet.cell(row=current_row, column=1).font = Font(bold=True, italic=True, size=9)
                    daily_sheet.cell(row=current_row, column=1).border = thin_border

                    running_total = 0
                    for day in range(1, days_in_month + 1):
                        col = day + 1
                        running_total += daily_values[day - 1]

                        cell = daily_sheet.cell(row=current_row, column=col)
                        cell.value = running_total if running_total > 0 else ""
                        cell.font = Font(size=8, italic=True)
                        cell.alignment = center_align
                        cell.border = thin_border
                        cell.fill = PatternFill(start_color="F0F8FF", end_color="F0F8FF", fill_type="solid")

                    current_row += 2  # Add space between collaborators

                # Add team global summary
                self._add_team_summary(daily_sheet, team_daily_values, month_name, year,
                                     days_in_month, current_row, thin_border, header_font, data_font, center_align)
            else:
                self.logger.warning("Team export: No CTJ data available")
                # Add a message in the sheet when no data is available
                daily_sheet.cell(row=4, column=1).value = "Aucune donnée disponible"
                daily_sheet.cell(row=4, column=1).font = Font(italic=True)
                daily_sheet.cell(row=4, column=1).border = thin_border
                current_row = 5

            # Auto-fit all columns based on content (now checking all rows including summary)
            for col in range(1, days_in_month + 2):
                # Get column letter safely
                from openpyxl.utils import get_column_letter
                column_letter = get_column_letter(col)

                # Calculate the maximum width needed for this column
                max_width = 0
                # Check all rows with content including summary sections
                for row in range(2, daily_sheet.max_row + 1):
                    cell = daily_sheet.cell(row=row, column=col)
                    if cell.value and not hasattr(cell, 'coordinate'):  # Skip merged cells
                        cell_width = len(str(cell.value)) + 2  # Add padding
                        max_width = max(max_width, cell_width)

                # Set minimum and maximum widths
                width = max(8, min(max_width, 30))  # Min 8, Max 30 (increased for summary content)
                daily_sheet.column_dimensions[column_letter].width = width

            # Make first column wider for collaborator names and labels
            daily_sheet.column_dimensions['A'].width = max(30, daily_sheet.column_dimensions['A'].width)

            self.logger.info("Team stacked export sheet created successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error creating team stacked export: {e}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    def _add_team_summary(self, daily_sheet, team_daily_values, month_name, year,
                         days_in_month, start_row, thin_border, header_font, data_font, center_align):
        """Add global summary section for team export."""
        try:
            from openpyxl.styles import Font, PatternFill

            # Calculate team statistics
            team_totals = []
            team_working_days = []
            collaborator_summaries = {}

            for collaborator, daily_values in team_daily_values.items():
                total_ctj = sum(daily_values)
                working_days = len([v for v in daily_values if v > 0])
                avg_ctj_per_working_day = total_ctj / working_days if working_days > 0 else 0
                max_daily_ctj = max(daily_values) if daily_values else 0

                collaborator_summaries[collaborator] = {
                    'total': total_ctj,
                    'working_days': working_days,
                    'avg_per_working_day': avg_ctj_per_working_day,
                    'max_daily': max_daily_ctj
                }

                team_totals.append(total_ctj)
                team_working_days.append(working_days)

            # Overall team statistics
            total_team_ctj = sum(team_totals)
            avg_team_working_days = sum(team_working_days) / len(team_working_days) if team_working_days else 0
            avg_team_ctj_per_month_day = total_team_ctj / days_in_month if days_in_month > 0 else 0
            max_team_daily = max([max(values) for values in team_daily_values.values()]) if team_daily_values else 0

            # Summary section styling
            summary_fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
            team_fill = PatternFill(start_color="D4EDDA", end_color="D4EDDA", fill_type="solid")  # Light green
            label_font = Font(size=10, bold=True)
            value_font = Font(size=10)

            # Title for summary section
            daily_sheet.merge_cells(f'A{start_row}:E{start_row}')
            title_cell = daily_sheet[f'A{start_row}']
            title_cell.value = f"📊 RÉSUMÉ GLOBAL ÉQUIPE - {month_name} {year}"
            title_cell.font = Font(size=12, bold=True, color="1F4E79")
            title_cell.alignment = center_align
            title_cell.fill = team_fill
            title_cell.border = thin_border

            current_row = start_row + 2

            # Individual collaborator summaries
            daily_sheet.merge_cells(f'A{current_row}:E{current_row}')
            collab_title_cell = daily_sheet[f'A{current_row}']
            collab_title_cell.value = "👥 DÉTAIL PAR COLLABORATEUR"
            collab_title_cell.font = Font(size=11, bold=True, color="2E7D32")
            collab_title_cell.alignment = center_align
            collab_title_cell.fill = summary_fill
            collab_title_cell.border = thin_border

            current_row += 1

            # Headers for collaborator details
            headers = ["Collaborateur", "CTJ Total", "Jours Travaillés", "CTJ Moy/Jour", "CTJ Max/Jour"]
            for col, header in enumerate(headers, 1):
                header_cell = daily_sheet.cell(row=current_row, column=col)
                header_cell.value = header
                header_cell.font = Font(size=9, bold=True)
                header_cell.alignment = center_align
                header_cell.border = thin_border
                header_cell.fill = summary_fill

            current_row += 1

            # Write collaborator details
            for collaborator, stats in sorted(collaborator_summaries.items()):
                collab_data = [
                    collaborator,
                    f"{stats['total']:,}",
                    f"{stats['working_days']}",
                    f"{stats['avg_per_working_day']:.1f}",
                    f"{stats['max_daily']}"
                ]

                for col, data in enumerate(collab_data, 1):
                    cell = daily_sheet.cell(row=current_row, column=col)
                    cell.value = data
                    cell.font = Font(size=9)
                    if col == 1:  # Collaborator name
                        cell.font = Font(size=9, bold=True)
                    cell.alignment = center_align if col > 1 else None
                    cell.border = thin_border

                current_row += 1

            self.logger.info(f"Added team summary: Total CTJ={total_team_ctj}, {len(team_daily_values)} collaborators")

        except Exception as e:
            self.logger.error(f"Error adding team summary: {e}")

    def _calculate_enhanced_daily_data(self, ctj_data, collaborator, month_name, year):
        """Calculate enhanced daily data with additional KPIs."""
        try:
            from datetime import datetime
            import calendar

            # Get month info
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            month_num = month_names.index(month_name) + 1
            year_num = int(year)
            days_in_month = calendar.monthrange(year_num, month_num)[1]

            enhanced_data = {}
            cumulative_ctj = 0

            # Get DMT for the collaborator
            collaborator_dmt = 0
            if collaborator in self.collaborator_stats:
                collaborator_dmt = self.collaborator_stats[collaborator].get('dmt', 0)
            elif collaborator == "Toute l'équipe":
                # Calculate average team DMT
                total_dmt = sum(stats.get('dmt', 0) for stats in self.collaborator_stats.values())
                active_collabs = len([stats for stats in self.collaborator_stats.values() if stats.get('dmt', 0) > 0])
                collaborator_dmt = total_dmt / active_collabs if active_collabs > 0 else 0

            for day in range(1, days_in_month + 1):
                day_data = {}

                if day <= len(ctj_data):
                    row_data = ctj_data[day - 1]

                    if collaborator == "Toute l'équipe":
                        # Team metrics
                        ctj_value = row_data.get('Total', 0)
                        day_data['Total'] = ctj_value
                        day_data['Communes_Started'] = self._estimate_communes_started(day, month_num, year_num)
                        day_data['Communes_Completed'] = self._estimate_communes_completed(day, month_num, year_num)
                        day_data['Active_Collabs'] = len([k for k, v in row_data.items() if k != 'Total' and v > 0])
                        day_data['Efficiency'] = ctj_value / collaborator_dmt if collaborator_dmt > 0 else 0
                    else:
                        # Individual metrics
                        ctj_value = row_data.get('CTJ', 0)
                        day_data['CTJ'] = ctj_value
                        day_data['DMT'] = collaborator_dmt
                        day_data['Active_Communes'] = self._estimate_active_communes(collaborator, day, month_num, year_num)
                        day_data['Efficiency'] = ctj_value / collaborator_dmt if collaborator_dmt > 0 else 0

                        cumulative_ctj += ctj_value
                        day_data['Cumulative_CTJ'] = cumulative_ctj
                else:
                    # No data for this day
                    if collaborator == "Toute l'équipe":
                        day_data = {'Total': 0, 'Communes_Started': 0, 'Communes_Completed': 0, 'Active_Collabs': 0, 'Efficiency': 0}
                    else:
                        day_data = {'CTJ': 0, 'DMT': collaborator_dmt, 'Active_Communes': 0, 'Efficiency': 0, 'Cumulative_CTJ': cumulative_ctj}

                enhanced_data[day] = day_data

            return enhanced_data

        except Exception as e:
            self.logger.error(f"Error calculating enhanced daily data: {e}")
            return {}

    def _estimate_communes_started(self, day, month, year):
        """Estimate communes started on a specific day (simplified)."""
        # This is a simplified estimation - in a real implementation,
        # you would track actual start dates from the data
        try:
            if 'Suivi Tickets' in self.global_suivi_data:
                df_tickets = self.global_suivi_data['Suivi Tickets']
                if not df_tickets.empty and 'Date d\'affectation' in df_tickets.columns:
                    # Filter by date (simplified - would need proper date parsing)
                    return 1 if day % 3 == 0 else 0  # Placeholder logic
            return 0
        except:
            return 0

    def _estimate_communes_completed(self, day, month, year):
        """Estimate communes completed on a specific day (simplified)."""
        # This is a simplified estimation - in a real implementation,
        # you would track actual completion dates from the data
        try:
            if 'Suivi Tickets' in self.global_suivi_data:
                df_tickets = self.global_suivi_data['Suivi Tickets']
                if not df_tickets.empty and 'Date Livraison' in df_tickets.columns:
                    # Filter by date (simplified - would need proper date parsing)
                    return 1 if day % 5 == 0 else 0  # Placeholder logic
            return 0
        except:
            return 0

    def _estimate_active_communes(self, collaborator, day, month, year):
        """Estimate active communes for a collaborator on a specific day."""
        # This is a simplified estimation - in a real implementation,
        # you would track actual active communes from the data
        try:
            if collaborator in self.collaborator_stats:
                # Estimate based on total workload (simplified)
                base_communes = 3  # Base number of active communes
                variation = (day % 7) - 3  # Daily variation
                return max(1, base_communes + variation)
            return 0
        except:
            return 0

    def _calculate_working_days_in_month(self, month_num, year_num):
        """
        Calculate the number of working days (excluding weekends) in a month.

        Args:
            month_num (int): Month number (1-12)
            year_num (int): Year number

        Returns:
            int: Number of working days (Monday to Friday)

        Examples:
            - June 2025 (30 days): 21 working days (30 - 9 weekend days)
            - February 2025 (28 days): 20 working days (28 - 8 weekend days)
        """
        try:
            import calendar
            from datetime import datetime

            # Get total days in month
            days_in_month = calendar.monthrange(year_num, month_num)[1]
            working_days = 0

            # Count working days (Monday=0 to Friday=4, Saturday=5, Sunday=6)
            for day in range(1, days_in_month + 1):
                date_obj = datetime(year_num, month_num, day)
                if date_obj.weekday() < 5:  # Monday to Friday (0-4)
                    working_days += 1

            self.logger.debug(f"Month {month_num}/{year_num}: {working_days} working days out of {days_in_month} total days")
            return working_days

        except Exception as e:
            self.logger.error(f"Error calculating working days: {e}")
            # Fallback: approximate 22 working days per month
            return 22

    def _create_commune_tracking_sheet(self, workbook, collaborator, month_name, year):
        """Create commune tracking sheet with detailed commune information."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # Create commune tracking sheet
            commune_sheet = workbook.create_sheet(title="Suivi Communes")

            # Define styles
            header_font = Font(size=11, bold=True, color="FFFFFF")
            data_font = Font(size=10)
            header_fill = PatternFill(start_color="1F4E79", end_color="1F4E79", fill_type="solid")
            completed_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
            in_progress_fill = PatternFill(start_color="FFD700", end_color="FFD700", fill_type="solid")
            pending_fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

            center_align = Alignment(horizontal="center", vertical="center")
            left_align = Alignment(horizontal="left", vertical="center")

            # Title
            commune_sheet.merge_cells('A1:J1')
            title_cell = commune_sheet['A1']
            title_cell.value = f"🏘️ SUIVI DÉTAILLÉ DES COMMUNES - {collaborator.upper()}"
            title_cell.font = Font(size=14, bold=True, color="FFFFFF")
            title_cell.fill = header_fill
            title_cell.alignment = center_align

            # Headers
            headers = [
                'Commune', 'Code INSEE', 'Collaborateur', 'Statut', 'Date Début',
                'Date Fin', 'Durée (min)', 'Éléments', 'DMT', 'Progression'
            ]

            for col, header in enumerate(headers, 1):
                cell = commune_sheet.cell(row=3, column=col)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_align

            # Get commune data
            commune_data = self._get_commune_tracking_data(collaborator, month_name, year)

            # Write data
            for row_idx, commune_info in enumerate(commune_data, 4):
                commune_sheet.cell(row=row_idx, column=1).value = commune_info.get('nom', 'N/A')
                commune_sheet.cell(row=row_idx, column=2).value = commune_info.get('insee', 'N/A')
                commune_sheet.cell(row=row_idx, column=3).value = commune_info.get('collaborateur', 'N/A')
                commune_sheet.cell(row=row_idx, column=4).value = commune_info.get('statut', 'N/A')
                commune_sheet.cell(row=row_idx, column=5).value = commune_info.get('date_debut', 'N/A')
                commune_sheet.cell(row=row_idx, column=6).value = commune_info.get('date_fin', 'N/A')
                commune_sheet.cell(row=row_idx, column=7).value = commune_info.get('duree', 0)
                commune_sheet.cell(row=row_idx, column=8).value = commune_info.get('elements', 0)
                commune_sheet.cell(row=row_idx, column=9).value = f"{commune_info.get('dmt', 0):.1f}"
                commune_sheet.cell(row=row_idx, column=10).value = f"{commune_info.get('progression', 0):.1f}%"

                # Apply status-based formatting
                status = commune_info.get('statut', '')
                for col in range(1, 11):
                    cell = commune_sheet.cell(row=row_idx, column=col)
                    cell.font = data_font
                    if col in [1, 3]:  # Left align for text columns
                        cell.alignment = left_align
                    else:
                        cell.alignment = center_align

                    if status == 'Traité':
                        cell.fill = completed_fill
                    elif status == 'En Cours':
                        cell.fill = in_progress_fill
                    elif status in ['En Attente', 'Rejeté']:
                        cell.fill = pending_fill

            # Auto-adjust column widths
            column_widths = [20, 12, 18, 12, 12, 12, 12, 10, 8, 12]
            for col, width in enumerate(column_widths, 1):
                commune_sheet.column_dimensions[commune_sheet.cell(row=1, column=col).column_letter].width = width

            self.logger.info("Commune tracking sheet created successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error creating commune tracking sheet: {e}")
            return False

    def _get_commune_tracking_data(self, collaborator, month_name, year):
        """Get commune tracking data for the specified collaborator and period."""
        try:
            commune_data = []

            if 'Suivi Tickets' not in self.global_suivi_data:
                return commune_data

            df_tickets = self.global_suivi_data['Suivi Tickets']
            if df_tickets.empty:
                return commune_data

            # Filter data for collaborator
            if collaborator == "Toute l'équipe":
                filtered_data = df_tickets
            else:
                filtered_data = df_tickets[df_tickets.get('Collaborateur', '') == collaborator]

            # Process each commune
            for _, row in filtered_data.iterrows():
                commune_info = {
                    'nom': row.get('Nom Commune', 'N/A'),
                    'insee': row.get('Code INSEE', 'N/A'),
                    'collaborateur': row.get('Collaborateur', 'N/A'),
                    'statut': row.get('Etat Ticket PA', 'N/A'),
                    'date_debut': row.get('Date d\'affectation', 'N/A'),
                    'date_fin': row.get('Date Livraison', 'N/A'),
                    'duree': row.get('Durée Finale', 0),
                    'elements': self._estimate_commune_elements(row),
                    'dmt': self._calculate_commune_dmt(row),
                    'progression': self._calculate_commune_progression(row)
                }
                commune_data.append(commune_info)

            return commune_data

        except Exception as e:
            self.logger.error(f"Error getting commune tracking data: {e}")
            return []

    def _estimate_commune_elements(self, commune_row):
        """Estimate number of elements for a commune."""
        # This would be calculated from actual data in a real implementation
        return 50  # Placeholder

    def _calculate_commune_dmt(self, commune_row):
        """Calculate DMT for a specific commune."""
        duree = commune_row.get('Durée Finale', 0)
        elements = self._estimate_commune_elements(commune_row)
        return duree / elements if elements > 0 else 0

    def _calculate_commune_progression(self, commune_row):
        """Calculate progression percentage for a commune."""
        status = commune_row.get('Etat Ticket PA', '')
        if status == 'Traité':
            return 100.0
        elif status == 'En Cours':
            return 75.0  # Estimated
        elif status == 'En Attente':
            return 25.0  # Estimated
        else:
            return 0.0













    def _update_export_filters(self):
        """Update export filter options based on loaded data."""
        try:
            if not hasattr(self, 'export_filters') or not self.collaborator_stats:
                return

            # Update collaborator list with "Toute l'équipe" option
            collaborators = ["Toute l'équipe"] + sorted(list(self.collaborator_stats.keys()))
            self.export_filters['collaborator']['values'] = collaborators

            # Set current month as default
            from datetime import datetime
            current_month = datetime.now().month
            month_names = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                          "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
            self.month_var.set(month_names[current_month - 1])

            self.logger.info(f"Export filters updated with {len(collaborators)} collaborators (including 'Toute l'équipe')")

        except Exception as e:
            self.logger.error(f"Error updating export filters: {e}")

    def _enable_export_buttons(self):
        """Enable export buttons and filters when data is loaded."""
        try:
            if hasattr(self, 'export_buttons'):
                for button in self.export_buttons.values():
                    button.config(state=tk.NORMAL)

            if hasattr(self, 'export_filters'):
                for filter_widget in self.export_filters.values():
                    filter_widget.config(state="readonly")

        except Exception as e:
            self.logger.error(f"Error enabling export buttons: {e}")

    def _disable_export_buttons(self):
        """Disable export buttons and filters when no data is available."""
        try:
            if hasattr(self, 'export_buttons'):
                for button in self.export_buttons.values():
                    button.config(state=tk.DISABLED)

            if hasattr(self, 'export_filters'):
                for filter_widget in self.export_filters.values():
                    filter_widget.config(state=tk.DISABLED)

        except Exception as e:
            self.logger.error(f"Error disabling export buttons: {e}")
