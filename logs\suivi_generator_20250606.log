2025-06-06 22:48:30 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 22:48:30 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 22:48:30 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 22:48:30 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 22:48:30 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 22:48:30 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 22:48:30 - main - INFO - setup_application:45 - ============================================================
2025-06-06 22:48:30 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 22:48:30 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 22:48:30 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 22:48:30 - main - INFO - setup_application:49 - ============================================================
2025-06-06 22:48:30 - main - INFO - main:99 - Creating application...
2025-06-06 22:48:31 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 22:48:31 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 22:48:32 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 22:48:32 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 22:48:32 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 22:48:32 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 22:48:32 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 22:48:32 - main - INFO - main:102 - Application created successfully
2025-06-06 22:48:32 - main - INFO - main:103 - Starting main loop...
2025-06-06 22:48:32 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 22:48:32 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 22:48:35 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 22:48:35 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 22:48:35 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 22:48:35 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 22:48:35 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 22:48:35 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 22:48:35 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 22:48:35 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 22:48:35 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-06 22:48:35 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-06 22:48:35 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 22:48:35 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 22:48:44 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx
2025-06-06 22:48:44 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx (16 rows)
2025-06-06 22:48:44 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 65026, Commune: ARIES ESPENAN
2025-06-06 22:48:44 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-06 22:48:44 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-06 22:48:44 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 22:48:48 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 22:48:48 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 22:48:48 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 22:48:48 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-06 22:48:48 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 62 rows remaining
2025-06-06 22:48:48 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 22:48:55 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_AID_65026.xlsx
2025-06-06 22:48:55 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-CM Adresse
2025-06-06 22:48:55 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-Plan Adressage
2025-06-06 22:48:55 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-Informations Commune
2025-06-06 22:48:55 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-06 22:48:55 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-06 22:48:55 - core.excel_generator - INFO - _add_duration_formula:373 - Duration formulas added
2025-06-06 22:48:55 - core.excel_generator - INFO - _add_commune_validations:412 - Commune validations added
2025-06-06 22:48:55 - core.excel_generator - INFO - _add_plan_adressage_validations:441 - Plan Adressage validations added
2025-06-06 22:48:56 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_AID_65026.xlsx
2025-06-06 23:04:28 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:04:28 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:04:28 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:04:28 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:04:28 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:04:28 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:04:28 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:04:28 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:04:28 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:04:28 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:04:28 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:04:28 - main - INFO - main:99 - Creating application...
2025-06-06 23:04:28 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:04:28 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:04:29 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:04:29 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:04:29 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:04:29 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:04:29 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:04:29 - main - INFO - main:102 - Application created successfully
2025-06-06 23:04:29 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:04:29 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:04:29 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:04:31 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:04:31 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:04:31 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:04:31 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:04:31 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:04:31 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:04:31 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:04:31 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:04:31 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-06 23:04:31 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-06 23:04:31 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:04:31 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:04:38 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 69180_Fiabilisation_voies_SAINT ANDRE LA COTE_20250602_1448_matrice_globale.xlsx
2025-06-06 23:04:39 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 69180_Fiabilisation_voies_SAINT ANDRE LA COTE_20250602_1448_matrice_globale.xlsx (19 rows)
2025-06-06 23:04:39 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 69180, Commune: SAINT ANDRE LA COTE
2025-06-06 23:04:39 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 69180
2025-06-06 23:04:39 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SAINT ANDRE LA COTE
2025-06-06 23:04:39 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:04:40 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:04:40 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:04:40 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:04:40 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 39 rows with 'à analyser' and empty key columns
2025-06-06 23:04:40 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 171 rows remaining
2025-06-06 23:04:40 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:04:50 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SAINT ANDRE LA COTE_AID_69180.xlsx
2025-06-06 23:04:50 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-CM Adresse
2025-06-06 23:04:50 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-Plan Adressage
2025-06-06 23:04:50 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: AID-Informations Commune
2025-06-06 23:04:50 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-06 23:04:50 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-06 23:04:50 - core.excel_generator - INFO - _add_duration_formula:373 - Duration formulas added
2025-06-06 23:04:50 - core.excel_generator - INFO - _add_commune_validations:412 - Commune validations added
2025-06-06 23:04:50 - core.excel_generator - INFO - _add_plan_adressage_validations:441 - Plan Adressage validations added
2025-06-06 23:04:50 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SAINT ANDRE LA COTE_AID_69180.xlsx
2025-06-06 23:08:36 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:08:36 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:08:36 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:08:36 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:08:36 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:08:36 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:08:36 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:08:36 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:08:36 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:08:36 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:08:36 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:08:36 - main - INFO - main:99 - Creating application...
2025-06-06 23:08:37 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:08:37 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:08:37 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:08:37 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:08:37 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:08:37 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:08:37 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:08:37 - main - INFO - main:102 - Application created successfully
2025-06-06 23:08:37 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:08:37 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:08:37 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:08:38 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:08:38 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:08:38 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:08:38 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:08:38 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:08:38 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:08:38 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:08:39 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:08:39 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-06 23:08:39 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-06 23:08:39 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:08:39 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:08:44 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-06 23:08:44 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-06 23:08:44 - ui.components.generation - INFO - reset:196 - Generation section reset
2025-06-06 23:08:44 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-06 23:08:44 - ui.modules.suivi_generator_module - INFO - _reset_module:506 - Module reset successfully
2025-06-06 23:08:50 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:08:51 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:08:51 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:08:51 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:08:51 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:08:51 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:08:54 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:08:54 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:08:54 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:08:54 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:08:54 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:08:54 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:08:58 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:08:58 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: ds-CM Adresse
2025-06-06 23:08:58 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: ds-Plan Adressage
2025-06-06 23:08:58 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: ds-Informations Commune
2025-06-06 23:08:58 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-06 23:08:58 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-06 23:08:58 - core.excel_generator - INFO - _add_duration_formula:373 - Duration formulas added
2025-06-06 23:08:58 - core.excel_generator - INFO - _add_commune_validations:412 - Commune validations added
2025-06-06 23:08:58 - core.excel_generator - INFO - _add_plan_adressage_validations:441 - Plan Adressage validations added
2025-06-06 23:08:58 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:09:00 - main - INFO - main:107 - Application closed normally
2025-06-06 23:17:57 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:17:57 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:17:57 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:17:57 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:17:57 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:17:57 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:17:57 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:17:57 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:17:57 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:17:57 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:17:57 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:17:57 - main - INFO - main:99 - Creating application...
2025-06-06 23:17:58 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:17:58 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:18:00 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:18:00 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:18:00 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:18:00 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:18:00 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:18:00 - main - INFO - main:102 - Application created successfully
2025-06-06 23:18:00 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:18:00 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:18:00 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:18:09 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:18:09 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:18:09 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:18:09 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:18:09 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:18:09 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:18:09 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:18:09 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:18:09 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:18:09 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:18:09 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:18:09 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:18:16 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:18:16 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:18:16 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:18:16 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:18:16 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:18:16 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:18:21 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:18:21 - core.excel_generator - ERROR - generate_excel_file:80 - Error generating Excel file: 'NoneType' object has no attribute 'copy'
2025-06-06 23:18:29 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:18:30 - core.excel_generator - ERROR - generate_excel_file:80 - Error generating Excel file: 'NoneType' object has no attribute 'copy'
2025-06-06 23:18:43 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:18:43 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:18:43 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:18:43 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:18:43 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:18:43 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:18:46 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:18:46 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-CM Adresse
2025-06-06 23:18:46 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Plan Adressage
2025-06-06 23:18:46 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Informations Commune
2025-06-06 23:18:46 - core.excel_generator - INFO - _add_data_validations:297 - Data validations added to CM Adresse sheet
2025-06-06 23:18:46 - core.excel_generator - INFO - _create_validation_sheet:323 - Validation sheet created
2025-06-06 23:18:46 - core.excel_generator - INFO - _add_duration_formula:374 - Duration formulas added
2025-06-06 23:18:46 - core.excel_generator - INFO - _add_commune_validations:413 - Commune validations added
2025-06-06 23:18:46 - core.excel_generator - INFO - _add_plan_adressage_validations:442 - Plan Adressage validations added
2025-06-06 23:18:46 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:19:32 - main - INFO - main:107 - Application closed normally
2025-06-06 23:22:03 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:22:03 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:22:03 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:22:03 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:22:03 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:22:03 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:22:03 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:22:03 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:22:03 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:22:03 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:22:03 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:22:03 - main - INFO - main:99 - Creating application...
2025-06-06 23:22:04 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:22:04 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:22:05 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:22:05 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:22:05 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:22:05 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:22:05 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:22:05 - main - INFO - main:102 - Application created successfully
2025-06-06 23:22:05 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:22:05 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:22:05 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:22:13 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:22:13 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:22:13 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:22:13 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:22:14 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:22:14 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:22:14 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:22:14 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:22:14 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:22:14 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:22:14 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:22:14 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:22:22 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:22:22 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:22:22 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:22:22 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:22:22 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:22:22 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:22:25 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:22:25 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:22:25 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:22:25 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:22:25 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:22:25 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:22:30 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:22:30 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-CM Adresse
2025-06-06 23:22:30 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Plan Adressage
2025-06-06 23:22:30 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Informations Commune
2025-06-06 23:22:30 - core.excel_generator - INFO - _add_data_validations:297 - Data validations added to CM Adresse sheet
2025-06-06 23:22:30 - core.excel_generator - INFO - _create_validation_sheet:323 - Validation sheet created
2025-06-06 23:22:30 - core.excel_generator - INFO - _add_duration_formula:375 - Duration formulas added
2025-06-06 23:22:30 - core.excel_generator - INFO - _add_commune_validations:414 - Commune validations added
2025-06-06 23:22:30 - core.excel_generator - INFO - _add_plan_adressage_validations:443 - Plan Adressage validations added
2025-06-06 23:22:31 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:23:59 - main - INFO - main:107 - Application closed normally
2025-06-06 23:25:44 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:25:44 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:25:44 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:25:44 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:25:44 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:25:44 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:25:44 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:25:44 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:25:44 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:25:44 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:25:44 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:25:44 - main - INFO - main:99 - Creating application...
2025-06-06 23:25:44 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:25:44 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:25:45 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:25:45 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:25:45 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:25:46 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:25:46 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:25:46 - main - INFO - main:102 - Application created successfully
2025-06-06 23:25:46 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:25:46 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:25:46 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:26:03 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:26:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:26:03 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:26:03 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:26:03 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:26:03 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:26:03 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:26:03 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:26:03 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:26:03 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:26:03 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:26:03 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:26:10 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:26:10 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:26:10 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:26:10 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:26:10 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:26:10 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:26:13 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats_automate.xlsx
2025-06-06 23:26:13 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:26:13 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats_automate.xlsx
2025-06-06 23:26:13 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 40 rows remaining
2025-06-06 23:26:13 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:26:15 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:26:15 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:26:15 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:26:15 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:26:15 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:26:15 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-06 23:26:18 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:26:19 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-CM Adresse
2025-06-06 23:26:19 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Plan Adressage
2025-06-06 23:26:19 - core.excel_generator - INFO - _apply_sheet_styling:230 - Styling applied to sheet: ds-Informations Commune
2025-06-06 23:26:19 - core.excel_generator - INFO - _add_data_validations:297 - Data validations added to CM Adresse sheet
2025-06-06 23:26:19 - core.excel_generator - INFO - _create_validation_sheet:323 - Validation sheet created
2025-06-06 23:26:19 - core.excel_generator - INFO - _add_duration_formula:375 - Duration formulas added
2025-06-06 23:26:19 - core.excel_generator - INFO - _add_commune_validations:414 - Commune validations added
2025-06-06 23:26:19 - core.excel_generator - INFO - _add_plan_adressage_validations:443 - Plan Adressage validations added
2025-06-06 23:26:19 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_ds_87193.xlsx
2025-06-06 23:43:43 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:43:43 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:43:43 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:43:43 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:43:43 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:43:43 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:43:43 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-06 23:43:43 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:43:43 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:43:43 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:43:43 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-06 23:43:43 - __main__ - INFO - main:99 - Creating application...
2025-06-06 23:43:45 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:43:45 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:43:46 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:43:46 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:43:46 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:43:46 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:43:46 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:43:46 - __main__ - INFO - main:102 - Application created successfully
2025-06-06 23:43:46 - __main__ - INFO - main:103 - Starting main loop...
2025-06-06 23:43:46 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:43:46 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:43:51 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:43:51 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:43:51 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:43:51 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:43:51 - ui.modules.suivi_generator_module - ERROR - _create_module_ui:120 - Error creating module UI: 'TEXT_SECONDARY'
2025-06-06 23:43:51 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:43:51 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:43:51 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:43:51 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:43:51 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:43:51 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:43:51 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:44:01 - ui.modules.suivi_generator_module - ERROR - _on_file_loaded:309 - Error processing file: 'NoneType' object has no attribute 'enable_generation'
2025-06-06 23:44:01 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:44:01 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:44:01 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:44:01 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:44:01 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:44:04 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:44:07 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:44:08 - ui.modules.suivi_generator_module - ERROR - _on_file_loaded:309 - Error processing file: 'NoneType' object has no attribute 'enable_generation'
2025-06-06 23:44:08 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:44:08 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:44:08 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:44:38 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:44:38 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:44:38 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:44:38 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:44:38 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:44:38 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:44:38 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:44:38 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:44:38 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:44:38 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:44:38 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:44:38 - main - INFO - main:99 - Creating application...
2025-06-06 23:44:39 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:44:39 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:44:40 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:44:40 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:44:40 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:44:40 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:44:40 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:44:40 - main - INFO - main:102 - Application created successfully
2025-06-06 23:44:40 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:44:40 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:44:40 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:44:45 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:44:45 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:44:45 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:44:45 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:44:45 - ui.modules.suivi_generator_module - ERROR - _create_module_ui:120 - Error creating module UI: 'TEXT_SECONDARY'
2025-06-06 23:44:45 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:44:45 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:44:45 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:44:45 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:44:45 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:44:45 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-06 23:44:45 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:44:50 - ui.modules.suivi_generator_module - ERROR - _on_file_loaded:309 - Error processing file: 'NoneType' object has no attribute 'enable_generation'
2025-06-06 23:44:50 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:44:50 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:44:50 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:44:50 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:44:50 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-06 23:47:14 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:47:14 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:47:14 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:47:14 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:47:14 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:47:14 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:47:14 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-06 23:47:14 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:47:14 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:47:14 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:47:14 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-06 23:47:14 - __main__ - INFO - main:99 - Creating application...
2025-06-06 23:47:15 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:47:15 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:47:16 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:47:16 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:47:16 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:47:17 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:47:17 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:47:17 - __main__ - INFO - main:102 - Application created successfully
2025-06-06 23:47:17 - __main__ - INFO - main:103 - Starting main loop...
2025-06-06 23:47:17 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:47:17 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:47:23 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:47:23 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:47:23 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:47:23 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:47:23 - ui.modules.suivi_generator_module - ERROR - _create_module_ui:120 - Error creating module UI: 'TEXT_SECONDARY'
2025-06-06 23:47:23 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:47:23 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:47:24 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:47:24 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:47:24 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:47:24 - ui.modules.suivi_generator_module - INFO - _restore_session:499 - Session restored successfully
2025-06-06 23:47:24 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:47:28 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:47:29 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:47:29 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:47:29 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:47:29 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:47:29 - ui.modules.suivi_generator_module - INFO - on_success:332 - MOAI file processed successfully
2025-06-06 23:47:32 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:47:32 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:47:32 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:47:32 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:47:32 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:47:32 - ui.modules.suivi_generator_module - INFO - on_success:354 - QGis file processed successfully
2025-06-06 23:49:33 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:49:33 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:49:33 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:49:33 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:49:33 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:49:33 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:49:33 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:49:33 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:49:33 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:49:33 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:49:33 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:49:33 - main - INFO - main:99 - Creating application...
2025-06-06 23:49:33 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:49:33 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:49:34 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:49:34 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:49:34 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:49:35 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:49:35 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:49:35 - main - INFO - main:102 - Application created successfully
2025-06-06 23:49:35 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:49:35 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:49:35 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:49:36 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:49:36 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:49:36 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:49:36 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:49:36 - ui.modules.suivi_generator_module - ERROR - _create_module_ui:120 - Error creating module UI: 'TEXT_SECONDARY'
2025-06-06 23:49:36 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:49:36 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:49:36 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:49:36 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:49:36 - ui.modules.suivi_generator_module - INFO - _restore_session:499 - Session restored successfully
2025-06-06 23:49:36 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:49:37 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:49:43 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:49:43 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:49:43 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:49:43 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:49:43 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:49:43 - ui.modules.suivi_generator_module - INFO - on_success:332 - MOAI file processed successfully
2025-06-06 23:49:46 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:49:46 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:49:46 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:49:46 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:49:46 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:49:46 - ui.modules.suivi_generator_module - INFO - on_success:354 - QGis file processed successfully
2025-06-06 23:49:53 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-06 23:49:53 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-06 23:49:53 - ui.modules.suivi_generator_module - ERROR - _reset_module:531 - Error resetting module: 'NoneType' object has no attribute 'reset'
2025-06-06 23:49:59 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:49:59 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:49:59 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:49:59 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:49:59 - ui.modules.suivi_generator_module - INFO - on_success:332 - MOAI file processed successfully
2025-06-06 23:50:02 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:50:02 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:50:02 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:50:02 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:50:02 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:50:02 - ui.modules.suivi_generator_module - INFO - on_success:354 - QGis file processed successfully
2025-06-06 23:50:29 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:50:29 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:50:30 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:50:30 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:50:30 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:50:30 - ui.modules.suivi_generator_module - INFO - cleanup:612 - Module cleanup completed
2025-06-06 23:50:30 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:50:30 - ui.modules.suivi_generator_module - ERROR - _create_module_ui:120 - Error creating module UI: 'TEXT_SECONDARY'
2025-06-06 23:50:30 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:50:30 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:50:30 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:50:30 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:50:30 - ui.modules.suivi_generator_module - INFO - _restore_session:499 - Session restored successfully
2025-06-06 23:50:30 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:50:31 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:50:35 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-06 23:50:35 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-06 23:50:35 - ui.modules.suivi_generator_module - ERROR - _reset_module:531 - Error resetting module: 'NoneType' object has no attribute 'reset'
2025-06-06 23:50:46 - main - INFO - main:107 - Application closed normally
2025-06-06 23:53:08 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:53:08 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:53:08 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:53:08 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:53:08 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:53:08 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:53:08 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:53:08 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:53:08 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:53:08 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:53:08 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:53:08 - main - INFO - main:99 - Creating application...
2025-06-06 23:53:08 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:53:08 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:53:10 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:53:10 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:53:10 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:53:10 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:53:10 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:53:10 - main - INFO - main:102 - Application created successfully
2025-06-06 23:53:10 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:53:10 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:53:10 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:53:17 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-06 23:53:17 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-06 23:53:17 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-06 23:53:17 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-06 23:53:17 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-06 23:53:17 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-06 23:53:17 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-06 23:53:17 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:53:17 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:53:17 - ui.modules.suivi_generator_module - INFO - _restore_session:499 - Session restored successfully
2025-06-06 23:53:17 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-06 23:53:18 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-06 23:53:27 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 20250530115346_Crea_adresse_V2_test_injection_Banbou .XLS
2025-06-06 23:53:28 - core.file_processor - ERROR - read_moai_file:70 - Error reading MOAI file: File contains no valid workbook part
2025-06-06 23:53:28 - utils.performance - ERROR - wrapper:48 - Operation 'file_read' failed after 0.25s: File contains no valid workbook part
2025-06-06 23:53:28 - utils.performance - ERROR - worker:111 - Async task failed: MOAI file processing - File contains no valid workbook part
2025-06-06 23:53:28 - ui.modules.suivi_generator_module - ERROR - on_error:335 - Error processing MOAI file: File contains no valid workbook part
2025-06-06 23:53:33 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-06 23:53:33 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-06 23:53:33 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-06 23:53:33 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-06 23:53:33 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-06 23:53:33 - ui.modules.suivi_generator_module - INFO - on_success:332 - MOAI file processed successfully
2025-06-06 23:53:37 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-06 23:53:37 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-06 23:53:37 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-06 23:53:37 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-06 23:53:37 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-06 23:53:37 - ui.modules.suivi_generator_module - INFO - on_success:354 - QGis file processed successfully
2025-06-06 23:53:38 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test
2025-06-06 23:53:38 - utils.file_utils - INFO - create_teams_folder:331 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test
2025-06-06 23:53:38 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test
2025-06-06 23:53:38 - utils.file_utils - INFO - get_teams_file_path:357 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test\Suivi_SURDOUX_test_87193.xlsx
2025-06-06 23:53:40 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test\Suivi_SURDOUX_test_87193.xlsx
2025-06-06 23:53:40 - core.excel_generator - INFO - _apply_sheet_styling:237 - Styling applied to sheet: test-CM Adresse
2025-06-06 23:53:40 - core.excel_generator - INFO - _apply_sheet_styling:237 - Styling applied to sheet: test-Plan Adressage
2025-06-06 23:53:40 - core.excel_generator - INFO - _apply_sheet_styling:237 - Styling applied to sheet: test-Informations Commune
2025-06-06 23:53:40 - core.excel_generator - INFO - _add_data_validations:304 - Data validations added to CM Adresse sheet
2025-06-06 23:53:40 - core.excel_generator - INFO - _create_validation_sheet:330 - Validation sheet created
2025-06-06 23:53:40 - core.excel_generator - INFO - _add_duration_formula:382 - Duration formulas added
2025-06-06 23:53:40 - core.excel_generator - INFO - _add_commune_validations:421 - Commune validations added
2025-06-06 23:53:40 - core.excel_generator - INFO - _add_plan_adressage_validations:450 - Plan Adressage validations added
2025-06-06 23:53:40 - core.excel_generator - INFO - generate_excel_file:83 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test\Suivi_SURDOUX_test_87193.xlsx
2025-06-06 23:53:44 - ui.components.generation - INFO - show_generation_complete:356 - Opened folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\SURDOUX_test
2025-06-06 23:53:57 - main - INFO - main:107 - Application closed normally
2025-06-06 23:55:43 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250606.log
2025-06-06 23:55:43 - root - INFO - setup_logging:79 - ==================================================
2025-06-06 23:55:43 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-06 23:55:43 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-06 23:55:43 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-06 23:55:43 - root - INFO - setup_logging:83 - ==================================================
2025-06-06 23:55:43 - main - INFO - setup_application:45 - ============================================================
2025-06-06 23:55:43 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-06 23:55:43 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-06 23:55:43 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-06 23:55:43 - main - INFO - setup_application:49 - ============================================================
2025-06-06 23:55:43 - main - INFO - main:99 - Creating application...
2025-06-06 23:55:44 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-06 23:55:44 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-06 23:55:45 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-06 23:55:45 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:55:45 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-06 23:55:45 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-06 23:55:45 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-06 23:55:45 - main - INFO - main:102 - Application created successfully
2025-06-06 23:55:45 - main - INFO - main:103 - Starting main loop...
2025-06-06 23:55:45 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-06 23:55:45 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-06 23:55:48 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-06 23:55:48 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-06 23:55:50 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-06 23:55:50 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-07 00:00:35 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-07 00:00:35 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-07 00:00:37 - main - INFO - main:107 - Application closed normally
