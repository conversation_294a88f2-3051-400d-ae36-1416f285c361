"""
Main window class for the Suivi Generator platform.
Now serves as the application shell with navigation system.
"""

import tkinter as tk
from tkinter import messagebox
import logging
import os
from typing import Optional

import sys
from pathlib import Path

# Ensure src directory is in path
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from config.constants import COLORS, UIConfig
from utils.file_utils import get_icon_path
from utils.lazy_imports import get_PIL
from .styles import StyleManager
from .navigation import NavigationManager, NavigationState
from .home_screen import HomeScreen
from .settings_screen import SettingsScreen
from .modules import SuiviGeneratorModule, SuiviGlobalModule, TeamStatsModule

logger = logging.getLogger(__name__)


class MainWindow:
    """Main application window with navigation system."""

    def __init__(self, root: tk.Tk):
        """
        Initialize the main window.

        Args:
            root: Root Tkinter window
        """
        self.root = root
        self.logger = logging.getLogger(__name__)

        # Navigation system
        self.navigation_manager = None

        # UI components
        self.style_manager = None

        self._setup_window()
        self._setup_navigation()

        # Post-initialization
        self.root.after(10, self._post_init)
    
    def _setup_window(self):
        """Set up the main window properties."""
        self.root.title("SofreTrack Pro - Sofrecom")

        # Lancer en plein écran (maximisé)
        self.root.state('zoomed')  # Windows
        # Alternative pour d'autres OS
        try:
            self.root.attributes('-zoomed', True)  # Linux
        except:
            try:
                self.root.attributes('-fullscreen', False)  # macOS fallback
                self.root.geometry("1200x800")  # Fallback size
            except:
                self.root.geometry("1200x800")  # Fallback final

        self.root.minsize(800, 600)
        self.root.configure(bg=COLORS['BG'])
        self.root.resizable(True, True)

        # Set window icon
        self.root.after(1, self._set_window_icon)

        # Initialize style manager
        self.style_manager = StyleManager(self.root)
        self.style_manager.setup_styles()

        self.logger.info("Main window configured")

    def _setup_navigation(self):
        """Set up the navigation system."""
        # Create navigation manager
        self.navigation_manager = NavigationManager(self.root)

        # Register modules
        self.navigation_manager.register_module(
            NavigationState.SUIVI_GENERATOR,
            SuiviGeneratorModule,
            "Générateur Suivi",
            "Traitement MOAI et QGis"
        )

        self.navigation_manager.register_module(
            NavigationState.SUIVI_GLOBAL,
            SuiviGlobalModule,
            "Suivi Global Tickets",
            "Agrégation des suivis de communes"
        )

        self.navigation_manager.register_module(
            NavigationState.TEAM_STATS,
            TeamStatsModule,
            "Statistiques Équipe",
            "Tableau de bord des performances de l'équipe"
        )

        # Register callbacks for simple screens
        self.navigation_manager.register_callback(
            NavigationState.HOME,
            self._show_home_screen
        )

        self.navigation_manager.register_callback(
            NavigationState.ABOUT,
            self._show_about_screen
        )

        # Start with home screen
        self.navigation_manager.navigate_to(NavigationState.HOME)

        self.logger.info("Navigation system initialized")
    
    def _show_home_screen(self):
        """Show the home screen."""
        content_frame = self.navigation_manager.get_content_frame()
        HomeScreen(content_frame, self.navigation_manager)
        self.navigation_manager.set_window_title("Accueil")

    def _show_about_screen(self):
        """Show the about screen."""
        content_frame = self.navigation_manager.get_content_frame()
        SettingsScreen(content_frame, self.navigation_manager)
        self.navigation_manager.set_window_title("À propos")
    
    def _post_init(self):
        """Post-initialization tasks."""
        self._center_window()
        self.logger.info("Main window initialization complete")

    def _center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _set_window_icon(self):
        """Set the window icon."""
        try:
            icon_path = get_icon_path()

            if not os.path.exists(icon_path):
                self.logger.warning(f"Icon file not found: {icon_path}")
                return

            if icon_path.lower().endswith('.ico'):
                self.root.iconbitmap(icon_path)
                self.logger.info("Window icon set successfully")
            else:
                # For PNG files, use PIL
                Image, ImageTk = get_PIL()
                with Image.open(icon_path) as img:
                    if img.size[0] > 64 or img.size[1] > 64:
                        img = img.resize((32, 32), Image.Resampling.LANCZOS)
                    icon_image = ImageTk.PhotoImage(img)
                    self.root.iconphoto(True, icon_image)
                    self.logger.info("Window icon set successfully (PNG)")

        except Exception as e:
            self.logger.warning(f"Failed to set window icon: {e}")

    def run(self):
        """Start the application main loop."""
        self.logger.info("Starting application main loop")
        self.root.mainloop()
