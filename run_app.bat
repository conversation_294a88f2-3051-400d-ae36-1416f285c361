@echo off
REM Startup script for Suivi Generator (Windows)
REM Double-click this file to run the application

title Suivi Generator - Plan Adressage

echo.
echo ============================================================
echo   Suivi Generator - Plan Adressage
echo   Generateur de fichier Excel structure
echo ============================================================
echo.

REM Try to run with python
python run_app.py

REM If that fails, try py
if errorlevel 1 (
    echo.
    echo Trying alternative Python launcher...
    py run_app.py
)

REM If that also fails, try python3
if errorlevel 1 (
    echo.
    echo Trying python3...
    python3 run_app.py
)

REM If all fail, show error
if errorlevel 1 (
    echo.
    echo ============================================================
    echo ERROR: Could not start the application
    echo ============================================================
    echo.
    echo Please make sure Python is installed and in your PATH.
    echo You can download Python from: https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
)

echo.
echo Press any key to close this window...
pause >nul
