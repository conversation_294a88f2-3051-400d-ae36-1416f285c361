#!/usr/bin/env python3
"""
Startup script for the Suivi Generator application.
This script provides a simple way to run the modular application.
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed (optimized)."""
    required_packages = {
        'pandas': 'pandas',
        'PIL': 'Pillow',
        'openpyxl': 'openpyxl',
        'tkinter': 'tkinter (usually included with Python)'
    }

    missing = []

    # Fast dependency check - only test imports
    dependencies_to_check = [
        ('pandas', 'import pandas'),
        ('Pillow', 'from PIL import Image, ImageTk'),
        ('openpyxl', 'import openpyxl'),
        ('tkinter', 'import tkinter')
    ]

    for pkg_name, import_statement in dependencies_to_check:
        try:
            exec(import_statement)
        except ImportError:
            missing.append(pkg_name)
    
    if missing:
        print("❌ Missing required dependencies:")
        for pkg in missing:
            install_name = required_packages.get(pkg, pkg)
            if pkg == 'tkinter':
                print(f"   • {pkg} - Install Python with tkinter support")
            else:
                print(f"   • {pkg} - Install with: pip install {install_name}")
        
        print("\nInstall all at once:")
        installable = [required_packages[pkg] for pkg in missing if pkg != 'tkinter']
        if installable:
            print(f"pip install {' '.join(installable)}")
        
        return False
    
    return True

def setup_environment():
    """Set up the environment for running the application."""
    # Get the directory containing this script
    script_dir = Path(__file__).parent.absolute()
    src_dir = script_dir / "src"
    
    # Check if src directory exists
    if not src_dir.exists():
        print(f"❌ Source directory not found: {src_dir}")
        print("Make sure you're running this script from the project root directory.")
        return False
    
    # Add src to Python path
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
    
    return True

def run_application():
    """Run the Suivi Generator application."""
    try:
        print("🚀 Starting Suivi Generator...")
        
        # Import and run the application
        from main import main
        main()
        
    except KeyboardInterrupt:
        print("\n👋 Application closed by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("\nFor detailed error information, check the logs directory.")
        return False
    
    return True

def main():
    """Main function to run the application with all checks."""
    print("=" * 60)
    print("🏢 Suivi Generator - Plan Adressage")
    print("   Générateur de fichier Excel structuré")
    print("=" * 60)
    
    # Check Python version
    print("🔍 Checking Python version...")
    if not check_python_version():
        input("\nPress Enter to exit...")
        sys.exit(1)
    print("✅ Python version OK")
    
    # Check dependencies
    print("\n🔍 Checking dependencies...")
    if not check_dependencies():
        input("\nPress Enter to exit...")
        sys.exit(1)
    print("✅ All dependencies available")
    
    # Setup environment
    print("\n🔧 Setting up environment...")
    if not setup_environment():
        input("\nPress Enter to exit...")
        sys.exit(1)
    print("✅ Environment ready")
    
    # Run application
    print("\n" + "=" * 60)
    if not run_application():
        input("\nPress Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
