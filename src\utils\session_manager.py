"""
Session management and auto-save functionality.
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages application sessions and auto-save functionality."""
    
    def __init__(self, session_dir: str = "sessions"):
        """
        Initialize the session manager.
        
        Args:
            session_dir: Directory to store session files
        """
        self.session_dir = Path(session_dir)
        self.session_dir.mkdir(exist_ok=True)
        self.current_session_file = self.session_dir / "current_session.json"
        self.logger = logging.getLogger(__name__)
        
        # Session data
        self.session_data = {
            'files': {'moai': None, 'qgis': None},
            'project_info': {'nom_commune': '', 'insee': '', 'id_tache': ''},
            'last_save': None,
            'window_geometry': None,
            'preferences': {}
        }
        
        # Load existing session
        self.load_session()
    
    def save_session(self, data: Optional[Dict[str, Any]] = None):
        """
        Save current session data.
        
        Args:
            data: Optional data to merge with current session
        """
        try:
            if data:
                self.session_data.update(data)
            
            self.session_data['last_save'] = datetime.now().isoformat()
            
            with open(self.current_session_file, 'w', encoding='utf-8') as f:
                json.dump(self.session_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("Session saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save session: {e}")
    
    def load_session(self) -> Dict[str, Any]:
        """
        Load session data.
        
        Returns:
            Session data dictionary
        """
        try:
            if self.current_session_file.exists():
                with open(self.current_session_file, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                    self.session_data.update(loaded_data)
                    self.logger.debug("Session loaded successfully")
            else:
                self.logger.debug("No existing session found, using defaults")
                
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
        
        return self.session_data.copy()
    
    def update_files(self, moai_file: Optional[str] = None, qgis_file: Optional[str] = None):
        """
        Update file paths in session.
        
        Args:
            moai_file: Path to MOAI file
            qgis_file: Path to QGis file
        """
        if moai_file is not None:
            self.session_data['files']['moai'] = moai_file
        if qgis_file is not None:
            self.session_data['files']['qgis'] = qgis_file
        
        self.save_session()
    
    def update_project_info(self, project_data: Dict[str, str]):
        """
        Update project information in session.
        
        Args:
            project_data: Project information dictionary
        """
        self.session_data['project_info'].update(project_data)
        self.save_session()
    
    def update_window_geometry(self, geometry: str):
        """
        Update window geometry in session.
        
        Args:
            geometry: Window geometry string
        """
        self.session_data['window_geometry'] = geometry
        self.save_session()
    
    def get_files(self) -> Dict[str, Optional[str]]:
        """Get saved file paths."""
        return self.session_data['files'].copy()
    
    def get_project_info(self) -> Dict[str, str]:
        """Get saved project information."""
        return self.session_data['project_info'].copy()
    
    def get_window_geometry(self) -> Optional[str]:
        """Get saved window geometry."""
        return self.session_data.get('window_geometry')
    
    def clear_session(self):
        """Clear current session data."""
        self.session_data = {
            'files': {'moai': None, 'qgis': None},
            'project_info': {'nom_commune': '', 'insee': '', 'id_tache': ''},
            'last_save': None,
            'window_geometry': None,
            'preferences': {}
        }
        self.save_session()
        self.logger.info("Session cleared")
    
    def export_session(self, file_path: str):
        """
        Export session to a file.
        
        Args:
            file_path: Path to export file
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.session_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Session exported to: {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to export session: {e}")
    
    def import_session(self, file_path: str):
        """
        Import session from a file.
        
        Args:
            file_path: Path to import file
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_data = json.load(f)
                self.session_data.update(imported_data)
                self.save_session()
            self.logger.info(f"Session imported from: {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to import session: {e}")


class AutoSaveManager:
    """Manages auto-save functionality."""
    
    def __init__(self, session_manager: SessionManager, interval: int = 30):
        """
        Initialize the auto-save manager.
        
        Args:
            session_manager: Session manager instance
            interval: Auto-save interval in seconds
        """
        self.session_manager = session_manager
        self.interval = interval * 1000  # Convert to milliseconds
        self.auto_save_enabled = True
        self.logger = logging.getLogger(__name__)
        self._timer_id = None
    
    def start_auto_save(self, root):
        """
        Start auto-save timer.
        
        Args:
            root: Tkinter root window
        """
        if self.auto_save_enabled:
            self._schedule_auto_save(root)
            self.logger.debug(f"Auto-save started with {self.interval/1000}s interval")
    
    def stop_auto_save(self, root):
        """
        Stop auto-save timer.
        
        Args:
            root: Tkinter root window
        """
        if self._timer_id:
            root.after_cancel(self._timer_id)
            self._timer_id = None
            self.logger.debug("Auto-save stopped")
    
    def _schedule_auto_save(self, root):
        """Schedule the next auto-save."""
        if self.auto_save_enabled:
            self._timer_id = root.after(self.interval, lambda: self._perform_auto_save(root))
    
    def _perform_auto_save(self, root):
        """Perform auto-save operation."""
        try:
            self.session_manager.save_session()
            self.logger.debug("Auto-save completed")
        except Exception as e:
            self.logger.error(f"Auto-save failed: {e}")
        finally:
            # Schedule next auto-save
            self._schedule_auto_save(root)
    
    def enable_auto_save(self, enabled: bool = True):
        """
        Enable or disable auto-save.
        
        Args:
            enabled: Whether to enable auto-save
        """
        self.auto_save_enabled = enabled
        self.logger.info(f"Auto-save {'enabled' if enabled else 'disabled'}")
    
    def set_interval(self, interval: int):
        """
        Set auto-save interval.
        
        Args:
            interval: Interval in seconds
        """
        self.interval = interval * 1000
        self.logger.info(f"Auto-save interval set to {interval} seconds")


class RecentFilesManager:
    """Manages recently used files."""
    
    def __init__(self, max_files: int = 10):
        """
        Initialize the recent files manager.
        
        Args:
            max_files: Maximum number of recent files to track
        """
        self.max_files = max_files
        self.recent_files = {'moai': [], 'qgis': []}
        self.logger = logging.getLogger(__name__)
        
        # Load recent files
        self._load_recent_files()
    
    def add_recent_file(self, file_path: str, file_type: str):
        """
        Add a file to recent files list.
        
        Args:
            file_path: Path to the file
            file_type: Type of file ('moai' or 'qgis')
        """
        if file_type not in self.recent_files:
            return
        
        # Remove if already exists
        if file_path in self.recent_files[file_type]:
            self.recent_files[file_type].remove(file_path)
        
        # Add to beginning
        self.recent_files[file_type].insert(0, file_path)
        
        # Limit to max_files
        self.recent_files[file_type] = self.recent_files[file_type][:self.max_files]
        
        # Save to file
        self._save_recent_files()
        
        self.logger.debug(f"Added recent {file_type} file: {os.path.basename(file_path)}")
    
    def get_recent_files(self, file_type: str) -> list:
        """
        Get recent files for a type.
        
        Args:
            file_type: Type of file ('moai' or 'qgis')
            
        Returns:
            List of recent file paths
        """
        return self.recent_files.get(file_type, []).copy()
    
    def _load_recent_files(self):
        """Load recent files from storage."""
        try:
            recent_files_path = Path("sessions") / "recent_files.json"
            if recent_files_path.exists():
                with open(recent_files_path, 'r', encoding='utf-8') as f:
                    self.recent_files = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load recent files: {e}")
    
    def _save_recent_files(self):
        """Save recent files to storage."""
        try:
            recent_files_path = Path("sessions") / "recent_files.json"
            recent_files_path.parent.mkdir(exist_ok=True)
            
            with open(recent_files_path, 'w', encoding='utf-8') as f:
                json.dump(self.recent_files, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save recent files: {e}")
