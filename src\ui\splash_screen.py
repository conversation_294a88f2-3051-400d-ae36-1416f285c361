"""
Splash screen for SofreTrack Pro application.

This module provides a professional splash screen that loads dependencies
while displaying progress to the user, using the same design language as the main application.
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import logging
from pathlib import Path
import os

# Defer heavy imports to avoid splash screen delay
logger = logging.getLogger(__name__)


class SplashScreen:
    """Professional splash screen with dependency loading and progress tracking."""
    
    def __init__(self, on_complete_callback=None):
        """
        Initialize the splash screen.
        
        Args:
            on_complete_callback: Function to call when loading is complete
        """
        self.on_complete_callback = on_complete_callback
        self.root = None
        self.progress_var = None
        self.status_var = None
        self.loading_complete = False
        self.error_occurred = False
        self.error_message = ""
        
        # Loading steps for progress tracking
        self.loading_steps = [
            ("Vérification des dépendances...", self._check_requirements),
            ("Initialisation de l'environnement...", self._init_environment),
            ("Chargement Des Bibliothèques...", self._load_pandas),
            ("Chargement Des Bibliothèques...", self._load_pil),
            ("Chargement Des Bibliothèques...", self._load_openpyxl),
            ("Configuration des modules...", self._setup_modules),
            ("Finalisation...", self._finalize_loading)
        ]
        
        self.current_step = 0
        self.total_steps = len(self.loading_steps)
        
        self.logger = logging.getLogger(__name__)
    
    def show(self):
        """Display the splash screen and start loading."""
        self._create_splash_window()

        # Show the splash screen immediately
        self.root.update()
        self.root.update_idletasks()

        # Small delay to ensure splash is visible before starting loading
        self.root.after(100, self._start_loading)
        self.root.mainloop()
    
    def _create_splash_window(self):
        """Create and configure the splash screen window."""
        # Import colors here to avoid delay at module load
        from config.constants import COLORS

        self.root = tk.Tk()
        self.root.title("SofreTrack Pro")

        # Window configuration
        window_width = 500
        window_height = 350

        # Center the window
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(False, False)
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.configure(bg=COLORS['WHITE'])

        # Skip icon loading for now to make splash appear faster

        self._create_splash_content()

        # Add subtle shadow effect
        self.root.wm_attributes("-topmost", True)
    
    def _create_splash_content(self):
        """Create the splash screen content with Sofrecom design language."""
        # Import constants here to avoid delay at module load
        from config.constants import COLORS, UIConfig, AppInfo

        # Main container with border
        main_frame = tk.Frame(
            self.root,
            bg=COLORS['WHITE'],
            relief='solid',
            bd=1,
            highlightbackground=COLORS['BORDER'],
            highlightthickness=1
        )
        main_frame.pack(fill='both', expand=True, padx=2, pady=2)

        # Header section with gradient-like effect
        header_frame = tk.Frame(main_frame, bg=COLORS['PRIMARY'], height=80)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)

        # App icon and title in header
        header_content = tk.Frame(header_frame, bg=COLORS['PRIMARY'])
        header_content.pack(expand=True, fill='both')

        # Try to load and display app icon
        self._add_app_icon(header_content)

        # App title
        title_label = tk.Label(
            header_content,
            text="SofreTrack Pro",
            font=("Segoe UI", 18, "bold"),
            fg=COLORS['WHITE'],
            bg=COLORS['PRIMARY']
        )
        title_label.pack(pady=(10, 5))

        # Subtitle
        subtitle_label = tk.Label(
            header_content,
            text="Solutions de traitement et génération de données",
            font=UIConfig.FONT_SUBTITLE,
            fg=COLORS['WHITE'],
            bg=COLORS['PRIMARY']
        )
        subtitle_label.pack()

        # Content area
        content_frame = tk.Frame(main_frame, bg=COLORS['WHITE'])
        content_frame.pack(fill='both', expand=True, padx=30, pady=(0, 30))

        # Loading status
        self.status_var = tk.StringVar(value="Initialisation...")
        status_label = tk.Label(
            content_frame,
            textvariable=self.status_var,
            font=UIConfig.FONT_SUBHEADER,
            fg=COLORS['INFO'],
            bg=COLORS['WHITE']
        )
        status_label.pack(pady=(20, 10))

        # Progress bar with Sofrecom styling
        progress_frame = tk.Frame(content_frame, bg=COLORS['WHITE'])
        progress_frame.pack(fill='x', pady=(0, 20))

        # Custom progress bar style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure(
            "Sofrecom.Horizontal.TProgressbar",
            background=COLORS['PRIMARY'],
            troughcolor=COLORS['LIGHT'],
            borderwidth=0,
            lightcolor=COLORS['PRIMARY'],
            darkcolor=COLORS['PRIMARY']
        )

        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            style="Sofrecom.Horizontal.TProgressbar",
            length=400
        )
        progress_bar.pack()

        # Progress percentage
        self.progress_text_var = tk.StringVar(value="0%")
        progress_text = tk.Label(
            content_frame,
            textvariable=self.progress_text_var,
            font=UIConfig.FONT_SMALL,
            fg=COLORS['TEXT_SECONDARY'],
            bg=COLORS['WHITE']
        )
        progress_text.pack(pady=(5, 0))

        # Footer with version and copyright
        footer_frame = tk.Frame(main_frame, bg=COLORS['LIGHT'], height=40)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)

        footer_content = tk.Frame(footer_frame, bg=COLORS['LIGHT'])
        footer_content.pack(expand=True, fill='both')

        version_label = tk.Label(
            footer_content,
            text=f"Version {AppInfo.VERSION} - {AppInfo.COPYRIGHT}",
            font=UIConfig.FONT_SMALL,
            fg=COLORS['TEXT_MUTED'],
            bg=COLORS['LIGHT']
        )
        version_label.pack(pady=10)
    
    def _add_app_icon(self, parent):
        """Add the application icon to the splash screen."""
        icon_frame = None
        try:
            # Import colors and file utils here
            from config.constants import COLORS
            from utils.file_utils import get_icon_path

            # Create icon container
            icon_frame = tk.Frame(parent, bg=COLORS['PRIMARY'])
            icon_frame.pack(side='left', padx=(20, 10), pady=10)

            # Try to load the actual app icon file
            self._load_app_icon_file(icon_frame)

        except Exception as e:
            self.logger.warning(f"Could not prepare app icon: {e}")
            # Fallback to emoji - create icon_frame if it doesn't exist
            try:
                from config.constants import COLORS
                if icon_frame is None:
                    icon_frame = tk.Frame(parent, bg=COLORS['PRIMARY'])
                    icon_frame.pack(side='left', padx=(20, 10), pady=10)

                icon_placeholder = tk.Label(
                    icon_frame,
                    text="🏢",
                    font=("Segoe UI", 28),
                    fg=COLORS['WHITE'],
                    bg=COLORS['PRIMARY'],
                    width=3,
                    height=2
                )
                icon_placeholder.pack()
            except Exception as fallback_error:
                self.logger.warning(f"Could not create fallback icon: {fallback_error}")
                # Last resort - create simple text without colors
                try:
                    if icon_frame is None:
                        icon_frame = tk.Frame(parent, bg="#366092")
                        icon_frame.pack(side='left', padx=(20, 10), pady=10)

                    simple_icon = tk.Label(
                        icon_frame,
                        text="🏢",
                        font=("Segoe UI", 28),
                        fg="#FFFFFF",
                        bg="#366092"
                    )
                    simple_icon.pack()
                except:
                    pass  # Give up on icon if everything fails
    
    def _start_loading(self):
        """Start the dependency loading process in a separate thread."""
        loading_thread = threading.Thread(target=self._load_dependencies, daemon=True)
        loading_thread.start()
    
    def _load_dependencies(self):
        """Load all dependencies with progress updates."""
        try:
            for i, (status_text, load_function) in enumerate(self.loading_steps):
                self.current_step = i
                
                # Update status
                self.root.after(0, lambda text=status_text: self.status_var.set(text))
                
                # Update progress
                progress = (i / self.total_steps) * 100
                self.root.after(0, lambda p=progress: self._update_progress(p))
                
                # Execute loading step
                try:
                    load_function()
                    # Small delay to show progress
                    time.sleep(0.3)
                except Exception as e:
                    self.logger.error(f"Error in loading step '{status_text}': {e}")
                    self.error_occurred = True
                    self.error_message = f"Erreur lors du chargement: {str(e)}"
                    break
            
            # Final progress update
            if not self.error_occurred:
                self.root.after(0, lambda: self._update_progress(100))
                self.root.after(0, lambda: self.status_var.set("Chargement terminé!"))
                time.sleep(0.5)
                self.loading_complete = True
            
            # Close splash screen and start main app
            self.root.after(100, self._finish_loading)
            
        except Exception as e:
            self.logger.error(f"Critical error during loading: {e}")
            self.error_occurred = True
            self.error_message = f"Erreur critique: {str(e)}"
            self.root.after(0, self._show_error)
    
    def _update_progress(self, value):
        """Update the progress bar and percentage text."""
        self.progress_var.set(value)
        self.progress_text_var.set(f"{int(value)}%")
        self.root.update_idletasks()
    
    def _finish_loading(self):
        """Finish loading and close splash screen."""
        if self.error_occurred:
            self._show_error()
        else:
            self.root.destroy()
            if self.on_complete_callback:
                self.on_complete_callback()
    
    def _show_error(self):
        """Show error message and close application."""
        import tkinter.messagebox as messagebox
        messagebox.showerror(
            "Erreur de chargement",
            f"Impossible de charger l'application:\n\n{self.error_message}\n\nVeuillez vérifier que toutes les dépendances sont installées."
        )
        self.root.destroy()

    # Loading step implementations
    def _check_requirements(self):
        """Check if all required dependencies are available."""
        # Fast dependency check - only check imports, don't load modules
        try:
            import pandas
            import openpyxl
            from PIL import Image
            import tkinter
        except ImportError as e:
            from utils.lazy_imports import get_missing_dependencies, install_instructions
            missing = get_missing_dependencies()
            error_msg = f"Dépendances manquantes: {', '.join(missing)}\n\n{install_instructions()}"
            raise Exception(error_msg)

    def _init_environment(self):
        """Initialize the application environment."""
        from utils.logging_config import setup_logging, configure_third_party_loggers
        from config.constants import AppInfo

        setup_logging(log_level="INFO", log_to_file=True)
        configure_third_party_loggers()

        # Log application startup information
        logger = logging.getLogger(__name__)
        logger.info("=" * 60)
        logger.info(f"Starting {AppInfo.DESCRIPTION}")
        logger.info(f"Version: {AppInfo.VERSION}")
        logger.info(f"Author: {AppInfo.AUTHOR}")
        logger.info("=" * 60)

    def _load_pandas(self):
        """Load pandas dependency."""
        import pandas as pd
        # Store in global cache for lazy imports
        from utils.lazy_imports import _import_cache
        _import_cache['pandas'] = pd

    def _load_pil(self):
        """Load PIL dependency."""
        from PIL import Image, ImageTk
        # Store in global cache
        from utils.lazy_imports import _import_cache
        _import_cache['PIL_Image'] = Image
        _import_cache['PIL_ImageTk'] = ImageTk

    def _load_openpyxl(self):
        """Load openpyxl dependency."""
        import openpyxl
        # Store in global cache
        from utils.lazy_imports import _import_cache
        _import_cache['openpyxl'] = openpyxl

    def _setup_modules(self):
        """Set up application modules."""
        # Pre-load any other necessary modules
        pass

    def _finalize_loading(self):
        """Finalize the loading process."""
        # Any final setup steps
        pass

    def _load_app_icon_file(self, parent):
        """Load the actual application icon file."""
        try:
            from config.constants import COLORS
            from PIL import Image, ImageTk
            import os

            # Get the project root directory (two levels up from src/ui/)
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

            # Try to find the icon file - check multiple possible locations
            icon_paths = [
                os.path.join(project_root, "Icone_App.png"),  # Exact case as found
                os.path.join(project_root, "Icone_App_Sharp.ico"),  # Exact case as found
                os.path.join(project_root, "icone_app.png"),  # Lowercase version
                os.path.join(project_root, "icone_app_sharp.ico"),  # Lowercase version
                os.path.join(project_root, "assets", "icone_app.png"),
                os.path.join(project_root, "assets", "icone_app_sharp.ico"),
                os.path.join(project_root, "src", "assets", "icone_app.png"),
                os.path.join(project_root, "src", "assets", "icone_app_sharp.ico")
            ]

            icon_image = None

            for icon_path in icon_paths:
                if icon_path and os.path.exists(icon_path):
                    try:
                        # Load and resize the icon
                        pil_image = Image.open(icon_path)
                        # Resize to 48x48 for splash screen
                        pil_image = pil_image.resize((48, 48), Image.Resampling.LANCZOS)
                        icon_image = ImageTk.PhotoImage(pil_image)
                        break
                    except Exception as e:
                        continue

            if icon_image:
                # Display the loaded icon
                icon_label = tk.Label(
                    parent,
                    image=icon_image,
                    bg=COLORS['PRIMARY']
                )
                icon_label.image = icon_image  # Keep a reference to prevent garbage collection
                icon_label.pack()
            else:
                # Fallback to geometric icon if no file found
                self._create_geometric_icon(parent)

        except Exception as e:
            self.logger.warning(f"Error loading app icon file: {e}")
            # Fallback to geometric icon
            self._create_geometric_icon(parent)

    def _create_geometric_icon(self, parent):
        """Create a simple geometric icon using Canvas for crisp display."""
        try:
            # Import colors here
            from config.constants import COLORS

            # Create a canvas for drawing the icon
            canvas = tk.Canvas(
                parent,
                width=48,
                height=48,
                bg=COLORS['PRIMARY'],
                highlightthickness=0
            )
            canvas.pack()

            # Draw a simple building-like icon
            # Base color - light blue
            base_color = "#4A90E2"
            accent_color = "#357ABD"

            # Main building rectangle
            canvas.create_rectangle(
                8, 16, 40, 44,
                fill=base_color,
                outline=accent_color,
                width=2
            )

            # Windows (small rectangles)
            window_color = "#FFFFFF"
            # Row 1
            canvas.create_rectangle(12, 20, 16, 24, fill=window_color, outline="")
            canvas.create_rectangle(20, 20, 24, 24, fill=window_color, outline="")
            canvas.create_rectangle(28, 20, 32, 24, fill=window_color, outline="")
            canvas.create_rectangle(36, 20, 40, 24, fill=window_color, outline="")

            # Row 2
            canvas.create_rectangle(12, 28, 16, 32, fill=window_color, outline="")
            canvas.create_rectangle(20, 28, 24, 32, fill=window_color, outline="")
            canvas.create_rectangle(28, 28, 32, 32, fill=window_color, outline="")
            canvas.create_rectangle(36, 28, 40, 32, fill=window_color, outline="")

            # Row 3
            canvas.create_rectangle(12, 36, 16, 40, fill=window_color, outline="")
            canvas.create_rectangle(20, 36, 24, 40, fill=window_color, outline="")
            canvas.create_rectangle(28, 36, 32, 40, fill=window_color, outline="")
            canvas.create_rectangle(36, 36, 40, 40, fill=window_color, outline="")

            # Roof triangle
            canvas.create_polygon(
                8, 16, 24, 4, 40, 16,
                fill=accent_color,
                outline=accent_color
            )

            self.logger.debug("Created geometric icon successfully")

        except Exception as e:
            self.logger.warning(f"Could not create geometric icon: {e}")
            # Fallback to text
            try:
                from config.constants import COLORS
            except:
                pass
            fallback_label = tk.Label(
                parent,
                text="🏢",
                font=("Segoe UI", 28),
                fg="#FFFFFF",  # Hardcode white as fallback
                bg="#366092"   # Hardcode primary color as fallback
            )
            fallback_label.pack()


