2025-06-07 00:09:48 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250607.log
2025-06-07 00:09:48 - root - INFO - setup_logging:79 - ==================================================
2025-06-07 00:09:48 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-07 00:09:48 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-07 00:09:48 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-07 00:09:48 - root - INFO - setup_logging:83 - ==================================================
2025-06-07 00:09:48 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-07 00:09:48 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-07 00:09:48 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-07 00:09:48 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-07 00:09:48 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-07 00:09:48 - __main__ - INFO - main:99 - Creating application...
2025-06-07 00:09:48 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-07 00:09:48 - ui.main_window - INFO - _setup_window:73 - Main window configured
2025-06-07 00:09:50 - ui.navigation - INFO - register_module:159 - Registered module: Générateur Suivi (suivi_generator)
2025-06-07 00:09:50 - ui.navigation - INFO - register_module:159 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-07 00:09:50 - ui.navigation - INFO - navigate_to:180 - Attempting to navigate to: home
2025-06-07 00:09:50 - ui.main_window - INFO - _set_window_icon:148 - Window icon set successfully
2025-06-07 00:09:50 - ui.navigation - INFO - navigate_to:215 - Successfully navigated to: home
2025-06-07 00:09:50 - ui.main_window - INFO - _setup_navigation:109 - Navigation system initialized
2025-06-07 00:09:50 - __main__ - INFO - main:102 - Application created successfully
2025-06-07 00:09:50 - __main__ - INFO - main:103 - Starting main loop...
2025-06-07 00:09:50 - ui.main_window - INFO - run:164 - Starting application main loop
2025-06-07 00:09:50 - ui.main_window - INFO - _post_init:126 - Main window initialization complete
2025-06-07 00:09:54 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global button
2025-06-07 00:09:54 - ui.navigation - INFO - navigate_to:180 - Attempting to navigate to: suivi_global
2025-06-07 00:09:54 - ui.navigation - INFO - _load_module:238 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-07 00:09:54 - ui.modules.suivi_global_module - INFO - _create_module_ui:61 - Suivi Global module UI created successfully
2025-06-07 00:09:54 - ui.navigation - INFO - _load_module:263 - Module suivi_global created and loaded successfully
2025-06-07 00:09:54 - ui.navigation - INFO - navigate_to:215 - Successfully navigated to: suivi_global
2025-06-07 00:10:07 - ui.navigation - INFO - navigate_to:180 - Attempting to navigate to: home
2025-06-07 00:10:07 - ui.navigation - INFO - navigate_to:215 - Successfully navigated to: home
2025-06-07 00:10:08 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-07 00:10:08 - ui.navigation - INFO - navigate_to:180 - Attempting to navigate to: suivi_generator
2025-06-07 00:10:08 - ui.navigation - INFO - _load_module:238 - Loading module: suivi_generator (Générateur Suivi)
2025-06-07 00:10:08 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-07 00:10:08 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-07 00:10:08 - ui.navigation - INFO - _load_module:263 - Module suivi_generator created and loaded successfully
2025-06-07 00:10:08 - ui.modules.suivi_generator_module - ERROR - _initialize_optional_features:105 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_generator'
2025-06-07 00:10:08 - ui.navigation - INFO - navigate_to:215 - Successfully navigated to: suivi_generator
2025-06-07 00:10:14 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-07 00:10:14 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-07 00:10:14 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-07 00:10:14 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-07 00:10:14 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-07 00:10:14 - ui.modules.suivi_generator_module - INFO - on_success:332 - MOAI file processed successfully
2025-06-07 00:10:17 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-07 00:10:17 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-07 00:10:17 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-07 00:10:17 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-07 00:10:17 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-07 00:10:17 - ui.modules.suivi_generator_module - INFO - on_success:354 - QGis file processed successfully
2025-06-07 00:10:44 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250607.log
2025-06-07 00:10:44 - root - INFO - setup_logging:79 - ==================================================
2025-06-07 00:10:44 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-07 00:10:44 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-07 00:10:44 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-07 00:10:44 - root - INFO - setup_logging:83 - ==================================================
2025-06-07 00:10:44 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-07 00:10:44 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-07 00:10:44 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-07 00:10:44 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-07 00:10:44 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-07 00:10:44 - __main__ - INFO - main:99 - Creating application...
2025-06-07 00:10:44 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-07 00:10:44 - ui.main_window - INFO - _setup_window:73 - Main window configured
2025-06-07 00:10:45 - ui.navigation - INFO - register_module:159 - Registered module: Générateur Suivi (suivi_generator)
2025-06-07 00:10:45 - ui.navigation - INFO - register_module:159 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-07 00:10:45 - ui.navigation - INFO - navigate_to:180 - Attempting to navigate to: home
2025-06-07 00:10:45 - ui.main_window - INFO - _set_window_icon:148 - Window icon set successfully
2025-06-07 00:10:46 - ui.navigation - INFO - navigate_to:215 - Successfully navigated to: home
2025-06-07 00:10:46 - ui.main_window - INFO - _setup_navigation:109 - Navigation system initialized
2025-06-07 00:10:46 - __main__ - INFO - main:102 - Application created successfully
2025-06-07 00:10:46 - __main__ - INFO - main:103 - Starting main loop...
2025-06-07 00:10:46 - ui.main_window - INFO - run:164 - Starting application main loop
2025-06-07 00:10:46 - ui.main_window - INFO - _post_init:126 - Main window initialization complete
