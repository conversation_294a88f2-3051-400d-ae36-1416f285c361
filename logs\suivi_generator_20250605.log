2025-06-05 12:12:08 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:12:08 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:12:08 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:12:08 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:12:08 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:12:08 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:12:08 - __main__ - INFO - setup_application:41 - ============================================================
2025-06-05 12:12:08 - __main__ - INFO - setup_application:42 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:12:08 - __main__ - INFO - setup_application:43 - Version: 2.0.0
2025-06-05 12:12:08 - __main__ - INFO - setup_application:44 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:12:08 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 12:12:08 - __main__ - INFO - main:95 - Creating application...
2025-06-05 12:12:10 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:12:10 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:12:11 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:12:11 - __main__ - INFO - main:98 - Application created successfully
2025-06-05 12:12:11 - __main__ - INFO - main:99 - Starting main loop...
2025-06-05 12:12:11 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:12:11 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:12:11 - utils.lazy_imports - INFO - get_PIL:48 - PIL loaded successfully
2025-06-05 12:12:12 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:12:12 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:12:40 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:12:40 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:12:40 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:12:40 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:12:40 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:12:40 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:12:40 - __main__ - INFO - setup_application:41 - ============================================================
2025-06-05 12:12:40 - __main__ - INFO - setup_application:42 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:12:40 - __main__ - INFO - setup_application:43 - Version: 2.0.0
2025-06-05 12:12:40 - __main__ - INFO - setup_application:44 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:12:40 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 12:12:40 - __main__ - INFO - main:95 - Creating application...
2025-06-05 12:12:41 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:12:41 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:12:42 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:12:42 - __main__ - INFO - main:98 - Application created successfully
2025-06-05 12:12:42 - __main__ - INFO - main:99 - Starting main loop...
2025-06-05 12:12:42 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:12:43 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:12:43 - utils.lazy_imports - INFO - get_PIL:48 - PIL loaded successfully
2025-06-05 12:12:43 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:12:43 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:16:18 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:16:18 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:16:18 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:16:18 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:16:18 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:16:18 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:16:18 - main - INFO - setup_application:41 - ============================================================
2025-06-05 12:16:18 - main - INFO - setup_application:42 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:16:18 - main - INFO - setup_application:43 - Version: 2.0.0
2025-06-05 12:16:18 - main - INFO - setup_application:44 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:16:18 - main - INFO - setup_application:45 - ============================================================
2025-06-05 12:16:18 - main - INFO - main:95 - Creating application...
2025-06-05 12:16:19 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:16:19 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:16:20 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:16:20 - main - INFO - main:98 - Application created successfully
2025-06-05 12:16:20 - main - INFO - main:99 - Starting main loop...
2025-06-05 12:16:20 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:16:21 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:16:21 - utils.lazy_imports - INFO - get_PIL:48 - PIL loaded successfully
2025-06-05 12:16:21 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:16:21 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:16:32 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:16:32 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:16:32 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:16:32 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:16:32 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:16:32 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:16:32 - main - INFO - setup_application:41 - ============================================================
2025-06-05 12:16:32 - main - INFO - setup_application:42 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:16:32 - main - INFO - setup_application:43 - Version: 2.0.0
2025-06-05 12:16:32 - main - INFO - setup_application:44 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:16:32 - main - INFO - setup_application:45 - ============================================================
2025-06-05 12:16:32 - main - INFO - main:95 - Creating application...
2025-06-05 12:16:33 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:16:33 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:16:34 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:16:34 - main - INFO - main:98 - Application created successfully
2025-06-05 12:16:34 - main - INFO - main:99 - Starting main loop...
2025-06-05 12:16:34 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:16:34 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:16:35 - utils.lazy_imports - INFO - get_PIL:48 - PIL loaded successfully
2025-06-05 12:16:35 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:16:36 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:16:37 - ui.components.file_import - INFO - _load_file:172 - No file selected
2025-06-05 12:16:38 - main - INFO - main:103 - Application closed normally
2025-06-05 12:16:40 - main - INFO - main:103 - Application closed normally
2025-06-05 12:19:57 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:19:57 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:19:57 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:19:57 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:19:57 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:19:57 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:19:57 - main - INFO - setup_application:41 - ============================================================
2025-06-05 12:19:57 - main - INFO - setup_application:42 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:19:57 - main - INFO - setup_application:43 - Version: 2.0.0
2025-06-05 12:19:57 - main - INFO - setup_application:44 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:19:57 - main - INFO - setup_application:45 - ============================================================
2025-06-05 12:19:57 - main - INFO - main:95 - Creating application...
2025-06-05 12:19:58 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:19:58 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:19:59 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:19:59 - main - INFO - main:98 - Application created successfully
2025-06-05 12:19:59 - main - INFO - main:99 - Starting main loop...
2025-06-05 12:19:59 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:19:59 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:19:59 - utils.lazy_imports - INFO - get_PIL:48 - PIL loaded successfully
2025-06-05 12:19:59 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:19:59 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:20:11 - utils.lazy_imports - INFO - get_pandas:28 - Pandas loaded successfully
2025-06-05 12:20:12 - core.file_processor - INFO - read_moai_file:49 - MOAI file loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx
2025-06-05 12:20:12 - core.file_processor - INFO - extract_insee_from_filename:126 - Extracted INSEE: 65026, Commune: ARIES ESPENAN
2025-06-05 12:20:12 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-05 12:20:12 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-05 12:20:12 - ui.main_window - INFO - _process_moai_file:211 - MOAI file processed successfully
2025-06-05 12:20:12 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx
2025-06-05 12:20:16 - core.file_processor - INFO - read_qgis_file:79 - QGis file loaded with column U
2025-06-05 12:20:16 - core.file_processor - INFO - read_qgis_file:86 - QGis file loaded successfully: resultats.xlsx
2025-06-05 12:20:16 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 12:20:16 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 62 rows remaining
2025-06-05 12:20:16 - ui.main_window - INFO - _process_qgis_file:226 - QGis file processed successfully
2025-06-05 12:20:16 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 12:20:24 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_test_65026.xlsx
2025-06-05 12:20:24 - core.excel_generator - INFO - _add_data_validations:218 - Data validations added to CM Adresse sheet
2025-06-05 12:20:24 - core.excel_generator - INFO - _create_validation_sheet:244 - Validation sheet created
2025-06-05 12:20:24 - core.excel_generator - INFO - _add_duration_formula:280 - Duration formulas added
2025-06-05 12:20:24 - core.excel_generator - INFO - _add_commune_validations:319 - Commune validations added
2025-06-05 12:20:24 - core.excel_generator - INFO - _add_plan_adressage_validations:348 - Plan Adressage validations added
2025-06-05 12:20:24 - core.excel_generator - INFO - generate_excel_file:70 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_test_65026.xlsx
2025-06-05 12:20:31 - ui.components.generation - INFO - show_generation_complete:280 - Opened folder: C:/Users/<USER>/OneDrive - orange.com/Bureau
2025-06-05 12:20:41 - main - INFO - main:103 - Application closed normally
2025-06-05 12:28:16 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 12:28:16 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 12:28:16 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 12:28:16 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 12:28:16 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 12:28:16 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 12:28:16 - main - INFO - setup_application:45 - ============================================================
2025-06-05 12:28:16 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 12:28:16 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 12:28:16 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 12:28:16 - main - INFO - setup_application:49 - ============================================================
2025-06-05 12:28:16 - main - INFO - main:99 - Creating application...
2025-06-05 12:28:16 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 12:28:16 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 12:28:18 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 12:28:18 - main - INFO - main:102 - Application created successfully
2025-06-05 12:28:18 - main - INFO - main:103 - Starting main loop...
2025-06-05 12:28:18 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 12:28:18 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 12:28:18 - utils.lazy_imports - INFO - get_PIL:57 - PIL loaded successfully
2025-06-05 12:28:18 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 12:28:18 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 12:28:25 - main - INFO - main:107 - Application closed normally
2025-06-05 16:47:29 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 16:47:29 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 16:47:29 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 16:47:29 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 16:47:29 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 16:47:29 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 16:47:29 - main - INFO - setup_application:45 - ============================================================
2025-06-05 16:47:29 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 16:47:29 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 16:47:29 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 16:47:29 - main - INFO - setup_application:49 - ============================================================
2025-06-05 16:47:29 - main - INFO - main:99 - Creating application...
2025-06-05 16:47:30 - ui.main_window - INFO - _setup_window:80 - Main window configured
2025-06-05 16:47:30 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 16:47:32 - ui.main_window - INFO - _setup_ui:101 - UI components created
2025-06-05 16:47:32 - main - INFO - main:102 - Application created successfully
2025-06-05 16:47:32 - main - INFO - main:103 - Starting main loop...
2025-06-05 16:47:32 - ui.main_window - INFO - run:302 - Starting application main loop
2025-06-05 16:47:33 - ui.main_window - INFO - _set_window_icon:162 - Window icon set successfully
2025-06-05 16:47:33 - utils.lazy_imports - INFO - get_PIL:57 - PIL loaded successfully
2025-06-05 16:47:33 - ui.components.header_footer - INFO - _load_logo_async:118 - Logo loaded successfully
2025-06-05 16:47:33 - ui.main_window - INFO - _post_init:140 - Main window initialization complete
2025-06-05 16:47:41 - ui.components.file_import - INFO - _load_file:172 - No file selected
2025-06-05 16:47:43 - main - INFO - main:107 - Application closed normally
2025-06-05 16:52:55 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 16:52:55 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 16:52:55 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 16:52:55 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 16:52:55 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 16:52:55 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 16:52:55 - main - INFO - setup_application:45 - ============================================================
2025-06-05 16:52:55 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 16:52:55 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 16:52:55 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 16:52:55 - main - INFO - setup_application:49 - ============================================================
2025-06-05 16:52:55 - main - INFO - main:99 - Creating application...
2025-06-05 16:52:55 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 16:52:55 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 16:52:57 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 16:52:57 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 16:52:57 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 16:52:57 - main - INFO - main:102 - Application created successfully
2025-06-05 16:52:57 - main - INFO - main:103 - Starting main loop...
2025-06-05 16:52:57 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 16:52:57 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 16:52:57 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 16:53:38 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 16:53:38 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 16:53:38 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 16:53:38 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 16:53:38 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 16:53:38 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 16:53:38 - main - INFO - setup_application:45 - ============================================================
2025-06-05 16:53:38 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 16:53:38 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 16:53:38 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 16:53:38 - main - INFO - setup_application:49 - ============================================================
2025-06-05 16:53:38 - main - INFO - main:99 - Creating application...
2025-06-05 16:53:39 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 16:53:39 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 16:53:40 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 16:53:40 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 16:53:40 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 16:53:40 - main - INFO - main:102 - Application created successfully
2025-06-05 16:53:40 - main - INFO - main:103 - Starting main loop...
2025-06-05 16:53:40 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 16:53:41 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 16:53:41 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 16:53:44 - ui.navigation - INFO - navigate_to:201 - Navigated to: settings
2025-06-05 16:53:58 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 16:54:31 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 16:54:36 - main - INFO - main:107 - Application closed normally
2025-06-05 17:01:15 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:01:15 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:01:15 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:01:15 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:01:15 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:01:15 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:01:15 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:01:15 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:01:15 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:01:15 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:01:15 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:01:15 - main - INFO - main:99 - Creating application...
2025-06-05 17:01:15 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:01:15 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:01:16 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:01:17 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 17:01:17 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:01:17 - main - INFO - main:102 - Application created successfully
2025-06-05 17:01:17 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:01:17 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:01:17 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:01:17 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:01:52 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:01:52 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:01:52 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:01:52 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:01:52 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:01:52 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:01:52 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:01:52 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:01:52 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:01:52 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:01:52 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:01:52 - main - INFO - main:99 - Creating application...
2025-06-05 17:01:53 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:01:53 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:01:54 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:01:54 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 17:01:54 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:01:54 - main - INFO - main:102 - Application created successfully
2025-06-05 17:01:54 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:01:54 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:01:55 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:01:55 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:02:56 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 17:03:07 - ui.navigation - INFO - navigate_to:201 - Navigated to: settings
2025-06-05 17:03:13 - ui.navigation - INFO - navigate_to:201 - Navigated to: home
2025-06-05 17:03:26 - main - INFO - main:107 - Application closed normally
2025-06-05 17:06:42 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:06:42 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:06:42 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:06:42 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:06:42 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:06:42 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:06:42 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:06:42 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:06:42 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:06:42 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:06:42 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:06:42 - main - INFO - main:99 - Creating application...
2025-06-05 17:06:43 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:06:43 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:06:45 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:06:45 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:06:45 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:06:46 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:06:46 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:06:46 - main - INFO - main:102 - Application created successfully
2025-06-05 17:06:46 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:06:46 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:06:46 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:08:18 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:08:18 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:08:18 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:08:18 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:08:18 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:08:18 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:08:18 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:08:18 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:08:18 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:08:18 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:08:18 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:08:18 - main - INFO - main:99 - Creating application...
2025-06-05 17:08:19 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:08:19 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:08:20 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:08:20 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:08:20 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:08:21 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:08:21 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:08:21 - main - INFO - main:102 - Application created successfully
2025-06-05 17:08:21 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:08:21 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:08:21 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:10:32 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:10:32 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:10:32 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:10:32 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:10:32 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:10:32 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:10:32 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:10:32 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:10:32 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:10:32 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:10:32 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:10:32 - main - INFO - main:99 - Creating application...
2025-06-05 17:10:33 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:10:33 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:10:33 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:10:33 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:10:34 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:10:34 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:10:34 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:10:34 - main - INFO - main:102 - Application created successfully
2025-06-05 17:10:34 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:10:34 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:10:34 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:11:24 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 17:11:24 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 17:11:25 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:11:26 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:11:32 - main - INFO - main:107 - Application closed normally
2025-06-05 17:14:34 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:14:34 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:14:34 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:14:34 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:14:34 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:14:34 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:14:34 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:14:34 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:14:34 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:14:34 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:14:34 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:14:34 - main - INFO - main:99 - Creating application...
2025-06-05 17:14:35 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:14:35 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:14:37 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:14:37 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:14:37 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:14:38 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:14:38 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:14:38 - main - INFO - main:102 - Application created successfully
2025-06-05 17:14:38 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:14:38 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:14:38 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:16:30 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:16:30 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:16:30 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:16:30 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:16:30 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:16:30 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:16:30 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:16:30 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:16:30 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:16:30 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:16:30 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:16:30 - main - INFO - main:99 - Creating application...
2025-06-05 17:16:31 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:16:31 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:16:32 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:16:32 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:16:32 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:16:32 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:16:32 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x000002989DA1DE80>>
2025-06-05 17:16:32 - ui.home_screen - INFO - _create_feature_card:224 - 🔥 DEBUG: Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:16:32 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:16:33 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:16:33 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:16:33 - main - INFO - main:102 - Application created successfully
2025-06-05 17:16:33 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:16:33 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:16:33 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:18:32 - main - INFO - main:107 - Application closed normally
2025-06-05 17:20:21 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:20:21 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:20:21 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:20:21 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:20:21 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:20:21 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:20:21 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:20:21 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:20:21 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:20:21 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:20:21 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:20:21 - main - INFO - main:99 - Creating application...
2025-06-05 17:20:21 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:20:21 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:20:22 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:20:22 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:20:22 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:20:23 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:20:23 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000022979711FD0>>
2025-06-05 17:20:23 - ui.home_screen - INFO - _create_feature_card:224 - 🔥 DEBUG: Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:20:23 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:20:23 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:20:23 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:20:23 - main - INFO - main:102 - Application created successfully
2025-06-05 17:20:23 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:20:23 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:20:23 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:21:10 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 17:21:10 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 17:21:10 - main - INFO - main:107 - Application closed normally
2025-06-05 17:21:57 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:21:57 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:21:57 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:21:57 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:21:57 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:21:57 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:21:57 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:21:57 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:21:57 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:21:57 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:21:57 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:21:57 - main - INFO - main:99 - Creating application...
2025-06-05 17:21:58 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:21:58 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:21:59 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:21:59 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:22:00 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:22:00 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:22:00 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000010A320C2120>>
2025-06-05 17:22:00 - ui.home_screen - INFO - _create_feature_card:224 - 🔥 DEBUG: Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:22:00 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:22:00 - ui.home_screen - INFO - _create_info_section:318 - 🔥 DEBUG: Simple test button created
2025-06-05 17:22:01 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:22:01 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:22:01 - main - INFO - main:102 - Application created successfully
2025-06-05 17:22:01 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:22:01 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:22:01 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:22:41 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:22:41 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:22:41 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000010A32094E10>>
2025-06-05 17:22:41 - ui.home_screen - INFO - _create_feature_card:224 - 🔥 DEBUG: Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:22:41 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:22:41 - ui.home_screen - INFO - _create_info_section:318 - 🔥 DEBUG: Simple test button created
2025-06-05 17:22:41 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:22:56 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:22:56 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:22:56 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000010A32095810>>
2025-06-05 17:22:56 - ui.home_screen - INFO - _create_feature_card:224 - 🔥 DEBUG: Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:22:56 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:22:56 - ui.home_screen - INFO - _create_info_section:318 - 🔥 DEBUG: Simple test button created
2025-06-05 17:22:56 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:22:57 - main - INFO - main:107 - Application closed normally
2025-06-05 17:24:50 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:24:50 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:24:50 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:24:50 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:24:50 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:24:50 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:24:50 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:24:50 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:24:50 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:24:50 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:24:50 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:24:50 - main - INFO - main:99 - Creating application...
2025-06-05 17:24:51 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:24:51 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:24:52 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:24:52 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:24:52 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:24:52 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:24:52 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CD91FD0>>
2025-06-05 17:24:52 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:24:52 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:24:52 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:24:52 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:24:52 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:24:52 - main - INFO - main:102 - Application created successfully
2025-06-05 17:24:52 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:24:52 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:24:52 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:25:22 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 17:25:22 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 17:25:23 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:25:23 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:25:23 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CD61810>>
2025-06-05 17:25:23 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:25:23 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:25:23 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:25:24 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:25:24 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:25:24 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:25:24 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CD61810>>
2025-06-05 17:25:24 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:25:24 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:25:24 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:25:24 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:25:25 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:25:25 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:25:25 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CDA0640>>
2025-06-05 17:25:25 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:25:25 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:25:25 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:25:25 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:26:04 - ui.home_screen - INFO - test_click:216 - 🔥 DEBUG: SUIVI GENERATOR BUTTON CLICKED!
2025-06-05 17:26:04 - ui.home_screen - INFO - _open_suivi_generator:369 - 🔥 DEBUG: User clicked Suivi Generator button
2025-06-05 17:26:04 - ui.home_screen - INFO - _open_suivi_generator:373 - 🔥 DEBUG: Navigation manager available, attempting navigation
2025-06-05 17:26:04 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 17:26:04 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 17:26:04 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 17:26:05 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 17:26:05 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 17:26:05 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 17:26:05 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 17:26:05 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 17:26:05 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 17:26:05 - ui.home_screen - INFO - _open_suivi_generator:376 - 🔥 DEBUG: Navigation call completed
2025-06-05 17:26:07 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:26:07 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:26:07 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CDA0640>>
2025-06-05 17:26:07 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:26:07 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:26:07 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:26:07 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:26:09 - ui.home_screen - INFO - test_click:216 - 🔥 DEBUG: SUIVI GENERATOR BUTTON CLICKED!
2025-06-05 17:26:09 - ui.home_screen - INFO - _open_suivi_generator:369 - 🔥 DEBUG: User clicked Suivi Generator button
2025-06-05 17:26:09 - ui.home_screen - INFO - _open_suivi_generator:373 - 🔥 DEBUG: Navigation manager available, attempting navigation
2025-06-05 17:26:09 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 17:26:09 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 17:26:09 - ui.modules.suivi_generator_module - INFO - cleanup:590 - Module cleanup completed
2025-06-05 17:26:10 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 17:26:10 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 17:26:10 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 17:26:10 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 17:26:10 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 17:26:10 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 17:26:10 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 17:26:10 - ui.home_screen - INFO - _open_suivi_generator:376 - 🔥 DEBUG: Navigation call completed
2025-06-05 17:26:12 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:26:12 - ui.home_screen - INFO - _create_features_section:109 - 🔥 DEBUG: Creating Suivi Generator feature card
2025-06-05 17:26:12 - ui.home_screen - INFO - _create_feature_card:210 - 🔥 DEBUG: Creating button for 📊 Générateur Suivi with command: <bound method HomeScreen._open_suivi_generator of <ui.home_screen.HomeScreen object at 0x0000027E5CD88B90>>
2025-06-05 17:26:12 - ui.home_screen - INFO - _create_feature_card:233 - 🔥 DEBUG: tk.Button created for 📊 Générateur Suivi, state: normal
2025-06-05 17:26:12 - ui.home_screen - INFO - _create_features_section:119 - 🔥 DEBUG: Suivi Generator feature card created
2025-06-05 17:26:12 - ui.home_screen - INFO - _create_info_section:325 - 🔥 DEBUG: Simple test button created
2025-06-05 17:26:12 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:26:38 - main - INFO - main:107 - Application closed normally
2025-06-05 17:31:01 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:31:01 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:31:01 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:31:01 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:31:01 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:31:01 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:31:01 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:31:01 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:31:01 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:31:01 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:31:01 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:31:01 - main - INFO - main:99 - Creating application...
2025-06-05 17:31:03 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:31:03 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:31:04 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:31:04 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:31:04 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:31:05 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:31:05 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:31:05 - main - INFO - main:102 - Application created successfully
2025-06-05 17:31:05 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:31:05 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:31:05 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:31:49 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 17:31:50 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 17:31:51 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:31:51 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:31:52 - main - INFO - main:107 - Application closed normally
2025-06-05 17:32:45 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:32:45 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:32:45 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:32:45 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:32:45 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:32:45 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:32:45 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:32:45 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:32:45 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:32:45 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:32:45 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:32:45 - main - INFO - main:99 - Creating application...
2025-06-05 17:32:46 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:32:46 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:32:48 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:32:48 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:32:48 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:32:49 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:32:49 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:32:49 - main - INFO - main:102 - Application created successfully
2025-06-05 17:32:49 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:32:49 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:32:49 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:34:01 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:34:01 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:34:01 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:34:01 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:34:01 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:34:01 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:34:01 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:34:01 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:34:01 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:34:01 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:34:01 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:34:01 - main - INFO - main:99 - Creating application...
2025-06-05 17:34:02 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:34:02 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:34:03 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:34:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:34:03 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:34:04 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:34:04 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:34:04 - main - INFO - main:102 - Application created successfully
2025-06-05 17:34:04 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:34:04 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:34:04 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:34:09 - ui.home_screen - INFO - _open_suivi_generator:354 - User clicked Suivi Generator button
2025-06-05 17:34:09 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 17:34:09 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 17:34:09 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 17:34:09 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 17:34:09 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 17:34:09 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 17:34:09 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 17:34:09 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 17:34:09 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 17:34:11 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:34:11 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:35:06 - main - INFO - main:107 - Application closed normally
2025-06-05 17:36:52 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:36:52 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:36:52 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:36:52 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:36:52 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:36:52 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:36:52 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:36:52 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:36:52 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:36:52 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:36:52 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:36:52 - main - INFO - main:99 - Creating application...
2025-06-05 17:36:53 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:36:53 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:36:54 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:36:54 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:36:54 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:36:55 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:36:55 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:36:55 - main - INFO - main:102 - Application created successfully
2025-06-05 17:36:55 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:36:55 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:36:55 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:37:16 - ui.home_screen - INFO - _open_suivi_generator:376 - User clicked Suivi Generator button
2025-06-05 17:37:16 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 17:37:16 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 17:37:16 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 17:37:16 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 17:37:16 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 17:37:16 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 17:37:16 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 17:37:16 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 17:37:16 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 17:37:18 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:37:18 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:40:52 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:40:52 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:40:52 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:40:52 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:40:52 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:40:52 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:40:52 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:40:52 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:40:52 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:40:52 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:40:52 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:40:52 - main - INFO - main:99 - Creating application...
2025-06-05 17:40:52 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:40:52 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:40:54 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:40:54 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:40:54 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:40:54 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:40:54 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:40:54 - main - INFO - main:102 - Application created successfully
2025-06-05 17:40:54 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:40:54 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:40:54 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:40:58 - ui.home_screen - INFO - _open_suivi_generator:388 - User clicked Suivi Generator button
2025-06-05 17:40:58 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 17:40:59 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 17:40:59 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 17:40:59 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 17:40:59 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 17:40:59 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 17:40:59 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 17:40:59 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 17:40:59 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 17:41:00 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:41:01 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:44:09 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:44:09 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:44:09 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:44:09 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:44:09 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:44:09 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:44:09 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:44:09 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:44:09 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:44:09 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:44:09 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:44:09 - main - INFO - main:99 - Creating application...
2025-06-05 17:44:10 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:44:10 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:44:11 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:44:11 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:44:11 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:44:12 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:44:12 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:44:12 - main - INFO - main:102 - Application created successfully
2025-06-05 17:44:12 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:44:12 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:44:12 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 17:49:28 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 17:49:28 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 17:49:28 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 17:49:28 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 17:49:28 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 17:49:28 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 17:49:28 - main - INFO - setup_application:45 - ============================================================
2025-06-05 17:49:28 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 17:49:28 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 17:49:28 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 17:49:28 - main - INFO - setup_application:49 - ============================================================
2025-06-05 17:49:28 - main - INFO - main:99 - Creating application...
2025-06-05 17:49:30 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 17:49:30 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 17:49:31 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 17:49:31 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 17:49:31 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 17:49:32 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 17:49:32 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 17:49:32 - main - INFO - main:102 - Application created successfully
2025-06-05 17:49:32 - main - INFO - main:103 - Starting main loop...
2025-06-05 17:49:32 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 17:49:32 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:03:37 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:03:37 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:03:37 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:03:37 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:03:37 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:03:37 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:03:37 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 18:03:37 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:03:37 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:03:37 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:03:37 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-05 18:03:37 - __main__ - INFO - main:99 - Creating application...
2025-06-05 18:03:38 - ui.main_window - INFO - _enter_fullscreen:152 - Entered fullscreen mode
2025-06-05 18:03:38 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:03:38 - ui.main_window - INFO - _setup_window:90 - Main window configured - Fullscreen: True
2025-06-05 18:03:39 - ui.navigation - INFO - register_module:230 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:03:39 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:39 - ui.main_window - INFO - _set_window_icon:194 - Window icon set successfully
2025-06-05 18:03:40 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:40 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:40 - ui.main_window - INFO - _setup_navigation:119 - Navigation system initialized
2025-06-05 18:03:40 - __main__ - INFO - main:102 - Application created successfully
2025-06-05 18:03:40 - __main__ - INFO - main:103 - Starting main loop...
2025-06-05 18:03:40 - ui.main_window - INFO - run:210 - Starting application main loop
2025-06-05 18:03:40 - ui.main_window - INFO - _post_init:136 - Main window initialization complete
2025-06-05 18:03:46 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:46 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:46 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:47 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:47 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:47 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:48 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:48 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:48 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:49 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: settings
2025-06-05 18:03:49 - ui.navigation - INFO - navigate_to:286 - Successfully navigated to: settings
2025-06-05 18:03:53 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:53 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:53 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:56 - ui.navigation - INFO - navigate_to:251 - Attempting to navigate to: home
2025-06-05 18:03:56 - ui.navigation - ERROR - navigate_to:289 - Navigation error: unknown color name "transparent"
2025-06-05 18:03:56 - ui.navigation - ERROR - navigate_to:291 - Full traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\navigation.py", line 277, in navigate_to
    self.callbacks[state](**kwargs)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\main_window.py", line 124, in _show_home_screen
    HomeScreen(content_frame, self.navigation_manager)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 33, in __init__
    self._create_home_screen()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 56, in _create_home_screen
    self._create_modern_hero_section(scrollable_frame)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\src\ui\home_screen.py", line 140, in _create_modern_hero_section
    cta_secondary = tk.Button(
        cta_container,
    ...<9 lines>...
        command=self._show_about
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2852, in __init__
    Widget.__init__(self, master, 'button', cnf, kw)
    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\tkinter\__init__.py", line 2774, in __init__
    self.tk.call(
    ~~~~~~~~~~~~^
        (widgetName, self._w) + extra + self._options(cnf))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: unknown color name "transparent"

2025-06-05 18:03:59 - __main__ - INFO - main:107 - Application closed normally
2025-06-05 18:06:24 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:06:24 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:06:24 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:06:24 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:06:24 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:06:24 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:06:24 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 18:06:24 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:06:24 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:06:24 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:06:24 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-05 18:06:24 - __main__ - INFO - main:99 - Creating application...
2025-06-05 18:06:24 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:06:24 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:06:26 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:06:26 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:06:26 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:06:27 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:06:27 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:06:27 - __main__ - INFO - main:102 - Application created successfully
2025-06-05 18:06:27 - __main__ - INFO - main:103 - Starting main loop...
2025-06-05 18:06:27 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:06:27 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:07:16 - __main__ - INFO - main:107 - Application closed normally
2025-06-05 18:08:49 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:08:49 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:08:49 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:08:49 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:08:49 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:08:49 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:08:49 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 18:08:49 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:08:49 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:08:49 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:08:49 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-05 18:08:49 - __main__ - INFO - main:99 - Creating application...
2025-06-05 18:08:50 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:08:50 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:08:51 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:08:51 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:08:51 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:08:52 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:08:52 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:08:52 - __main__ - INFO - main:102 - Application created successfully
2025-06-05 18:08:52 - __main__ - INFO - main:103 - Starting main loop...
2025-06-05 18:08:52 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:08:52 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:09:04 - __main__ - INFO - main:107 - Application closed normally
2025-06-05 18:12:04 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:12:04 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:12:04 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:12:04 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:12:04 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:12:04 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:12:04 - __main__ - INFO - setup_application:45 - ============================================================
2025-06-05 18:12:04 - __main__ - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:12:04 - __main__ - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:12:04 - __main__ - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:12:04 - __main__ - INFO - setup_application:49 - ============================================================
2025-06-05 18:12:04 - __main__ - INFO - main:99 - Creating application...
2025-06-05 18:12:05 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:12:05 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:12:06 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:12:06 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:12:06 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:12:07 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:12:07 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:12:07 - __main__ - INFO - main:102 - Application created successfully
2025-06-05 18:12:07 - __main__ - INFO - main:103 - Starting main loop...
2025-06-05 18:12:07 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:12:07 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:12:09 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:12:09 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:12:09 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:12:09 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:12:10 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:12:10 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:12:10 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:12:10 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:12:10 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:12:10 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:12:11 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:12:12 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:12:12 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:12:12 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:12:12 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:12:12 - ui.modules.suivi_generator_module - INFO - cleanup:590 - Module cleanup completed
2025-06-05 18:12:12 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:12:12 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:12:12 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:12:12 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:12:12 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:12:12 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:12:13 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:12:14 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:12:14 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:12:16 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:12:16 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:12:16 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:12:16 - ui.modules.suivi_generator_module - INFO - cleanup:590 - Module cleanup completed
2025-06-05 18:12:16 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:12:16 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:12:16 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:12:16 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:12:16 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:12:16 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:12:16 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:12:18 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:12:18 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:12:19 - __main__ - INFO - main:107 - Application closed normally
2025-06-05 18:34:58 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:34:58 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:34:58 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:34:58 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:34:58 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:34:58 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:34:58 - main - INFO - setup_application:45 - ============================================================
2025-06-05 18:34:58 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:34:58 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:34:58 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:34:58 - main - INFO - setup_application:49 - ============================================================
2025-06-05 18:34:58 - main - INFO - main:99 - Creating application...
2025-06-05 18:34:58 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:34:58 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:34:59 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:34:59 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:34:59 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:34:59 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:34:59 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:34:59 - main - INFO - main:102 - Application created successfully
2025-06-05 18:34:59 - main - INFO - main:103 - Starting main loop...
2025-06-05 18:34:59 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:34:59 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:35:40 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:35:40 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:35:40 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:35:40 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:35:40 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:35:40 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:35:40 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:35:40 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:35:40 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:35:40 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:35:57 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 18:35:57 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 18:35:57 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 18:35:57 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 18:35:57 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 18:35:57 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 18:36:00 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 18:36:00 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 18:36:00 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 18:36:01 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 18:36:01 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 18:36:01 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 18:36:08 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122222_09117.xlsx
2025-06-05 18:36:08 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 122222-CM Adresse
2025-06-05 18:36:08 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 122222-Plan Adressage
2025-06-05 18:36:08 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 122222-Informations Commune
2025-06-05 18:36:08 - core.excel_generator - INFO - _add_data_validations:309 - Data validations added to CM Adresse sheet
2025-06-05 18:36:08 - core.excel_generator - INFO - _create_validation_sheet:335 - Validation sheet created
2025-06-05 18:36:08 - core.excel_generator - INFO - _add_duration_formula:378 - Duration formulas added
2025-06-05 18:36:08 - core.excel_generator - INFO - _add_commune_validations:417 - Commune validations added
2025-06-05 18:36:08 - core.excel_generator - INFO - _add_plan_adressage_validations:446 - Plan Adressage validations added
2025-06-05 18:36:08 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122222_09117.xlsx
2025-06-05 18:45:29 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:45:29 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:45:29 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:45:29 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:45:29 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:45:29 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:45:29 - main - INFO - setup_application:45 - ============================================================
2025-06-05 18:45:29 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:45:29 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:45:29 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:45:29 - main - INFO - setup_application:49 - ============================================================
2025-06-05 18:45:29 - main - INFO - main:99 - Creating application...
2025-06-05 18:45:30 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:45:30 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:45:31 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:45:31 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:45:31 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:45:31 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:45:31 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:45:31 - main - INFO - main:102 - Application created successfully
2025-06-05 18:45:31 - main - INFO - main:103 - Starting main loop...
2025-06-05 18:45:31 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:45:31 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:45:39 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:45:39 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:45:39 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:45:39 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:45:39 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:45:39 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:45:39 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:45:39 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:45:39 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 18:45:39 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 18:45:39 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:45:39 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:45:56 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-05 18:45:56 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-05 18:45:56 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-05 18:45:56 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-05 18:45:56 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-05 18:45:56 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 18:46:00 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 18:46:00 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 18:46:00 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 18:46:00 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-05 18:46:00 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-05 18:46:00 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 18:46:06 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_1222225_87193.xlsx
2025-06-05 18:46:06 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-CM Adresse
2025-06-05 18:46:06 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-Plan Adressage
2025-06-05 18:46:06 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-Informations Commune
2025-06-05 18:46:06 - core.excel_generator - INFO - _add_data_validations:309 - Data validations added to CM Adresse sheet
2025-06-05 18:46:06 - core.excel_generator - INFO - _create_validation_sheet:335 - Validation sheet created
2025-06-05 18:46:06 - core.excel_generator - INFO - _add_duration_formula:388 - Duration formulas added
2025-06-05 18:46:06 - core.excel_generator - INFO - _add_commune_validations:427 - Commune validations added
2025-06-05 18:46:06 - core.excel_generator - INFO - _add_plan_adressage_validations:456 - Plan Adressage validations added
2025-06-05 18:46:06 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_1222225_87193.xlsx
2025-06-05 18:48:45 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:48:45 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:48:45 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:48:45 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:48:45 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:48:45 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:48:45 - main - INFO - setup_application:45 - ============================================================
2025-06-05 18:48:45 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:48:45 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:48:45 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:48:45 - main - INFO - setup_application:49 - ============================================================
2025-06-05 18:48:45 - main - INFO - main:99 - Creating application...
2025-06-05 18:48:46 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:48:46 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:48:47 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:48:47 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:48:47 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:48:47 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:48:47 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:48:47 - main - INFO - main:102 - Application created successfully
2025-06-05 18:48:47 - main - INFO - main:103 - Starting main loop...
2025-06-05 18:48:47 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:48:47 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:48:55 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:48:55 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:48:55 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:48:55 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:48:55 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:48:55 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:48:55 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:48:55 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:48:55 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-05 18:48:55 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-05 18:48:55 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:48:55 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:49:02 - ui.components.file_import - INFO - _load_file:172 - No file selected
2025-06-05 18:49:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 18:49:04 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 18:49:20 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:49:21 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:49:22 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:49:22 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:49:22 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:49:22 - ui.modules.suivi_generator_module - INFO - cleanup:590 - Module cleanup completed
2025-06-05 18:49:22 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:49:22 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:49:22 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:49:22 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:49:22 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:49:22 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-05 18:49:22 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-05 18:49:22 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:49:22 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:49:30 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-05 18:49:30 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-05 18:49:30 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-05 18:49:30 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 18:49:30 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 18:49:30 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 18:49:34 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 18:49:34 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 18:49:34 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 18:49:34 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 18:49:34 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-05 18:49:34 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 18:49:40 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_1222225_79312.xlsx
2025-06-05 18:49:40 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-CM Adresse
2025-06-05 18:49:40 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-Plan Adressage
2025-06-05 18:49:40 - core.excel_generator - INFO - _apply_sheet_styling:242 - Styling applied to sheet: 1222225-Informations Commune
2025-06-05 18:49:40 - core.excel_generator - INFO - _add_data_validations:309 - Data validations added to CM Adresse sheet
2025-06-05 18:49:40 - core.excel_generator - INFO - _create_validation_sheet:335 - Validation sheet created
2025-06-05 18:49:40 - core.excel_generator - INFO - _add_duration_formula:378 - Duration formulas added
2025-06-05 18:49:40 - core.excel_generator - INFO - _add_commune_validations:417 - Commune validations added
2025-06-05 18:49:40 - core.excel_generator - INFO - _add_plan_adressage_validations:446 - Plan Adressage validations added
2025-06-05 18:49:40 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_1222225_79312.xlsx
2025-06-05 18:52:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:52:03 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:52:04 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 18:52:04 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 18:52:13 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:52:14 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:57:11 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 18:57:11 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 18:57:11 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 18:57:11 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 18:57:11 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 18:57:11 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 18:57:11 - main - INFO - setup_application:45 - ============================================================
2025-06-05 18:57:11 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 18:57:11 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 18:57:11 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 18:57:11 - main - INFO - setup_application:49 - ============================================================
2025-06-05 18:57:11 - main - INFO - main:99 - Creating application...
2025-06-05 18:57:11 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 18:57:11 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 18:57:12 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 18:57:12 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 18:57:12 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 18:57:12 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 18:57:12 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 18:57:12 - main - INFO - main:102 - Application created successfully
2025-06-05 18:57:12 - main - INFO - main:103 - Starting main loop...
2025-06-05 18:57:12 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 18:57:12 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 18:57:17 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 18:57:17 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 18:57:17 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 18:57:17 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 18:57:17 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 18:57:17 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 18:57:17 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 18:57:17 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 18:57:17 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-05 18:57:17 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-05 18:57:17 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 18:57:17 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 18:57:26 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 18:57:26 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 18:57:26 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 18:57:26 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 18:57:26 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 18:57:26 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 18:57:29 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 18:57:29 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 18:57:29 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 18:57:29 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 18:57:29 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 18:57:29 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 18:57:34 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1222225_09117.xlsx
2025-06-05 18:57:34 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1222225-CM Adresse
2025-06-05 18:57:34 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1222225-Plan Adressage
2025-06-05 18:57:34 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1222225-Informations Commune
2025-06-05 18:57:34 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 18:57:34 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 18:57:34 - core.excel_generator - INFO - _add_duration_formula:372 - Duration formulas added
2025-06-05 18:57:34 - core.excel_generator - INFO - _add_commune_validations:411 - Commune validations added
2025-06-05 18:57:34 - core.excel_generator - INFO - _add_plan_adressage_validations:440 - Plan Adressage validations added
2025-06-05 18:57:34 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1222225_09117.xlsx
2025-06-05 19:04:58 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:04:58 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:04:58 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:04:58 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:04:58 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:04:58 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:04:58 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:04:58 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:04:58 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:04:58 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:04:58 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:04:58 - main - INFO - main:99 - Creating application...
2025-06-05 19:04:59 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:04:59 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:05:00 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:05:00 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:05:00 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:05:00 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:05:00 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:05:00 - main - INFO - main:102 - Application created successfully
2025-06-05 19:05:00 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:05:00 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:05:00 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:05:02 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:05:02 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:05:02 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:05:02 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:05:02 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:05:02 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:05:03 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:05:03 - utils.session_manager - ERROR - load_session:80 - Failed to load session: Expecting value: line 1 column 1 (char 0)
2025-06-05 19:05:03 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:05:03 - utils.session_manager - ERROR - load_session:80 - Failed to load session: Expecting value: line 1 column 1 (char 0)
2025-06-05 19:05:03 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:05:03 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:05:18 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-05 19:05:18 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-05 19:05:18 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-05 19:05:18 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 19:05:18 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 19:05:18 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:05:22 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:05:22 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats_automate.xlsx
2025-06-05 19:05:22 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats_automate.xlsx
2025-06-05 19:05:22 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 17 rows remaining
2025-06-05 19:05:22 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:05:24 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:05:24 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:05:24 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:05:24 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 19:05:24 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-05 19:05:24 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:05:31 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_122_79312.xlsx
2025-06-05 19:05:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122-CM Adresse
2025-06-05 19:05:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122-Plan Adressage
2025-06-05 19:05:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122-Informations Commune
2025-06-05 19:05:32 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 19:05:32 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 19:05:32 - core.excel_generator - INFO - _add_duration_formula:369 - Duration formulas added
2025-06-05 19:05:32 - core.excel_generator - INFO - _add_commune_validations:408 - Commune validations added
2025-06-05 19:05:32 - core.excel_generator - INFO - _add_plan_adressage_validations:437 - Plan Adressage validations added
2025-06-05 19:05:32 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_122_79312.xlsx
2025-06-05 19:05:34 - ui.components.generation - INFO - show_generation_complete:280 - Opened folder: C:/Users/<USER>/OneDrive - orange.com/Bureau
2025-06-05 19:12:41 - main - INFO - main:107 - Application closed normally
2025-06-05 19:18:53 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:18:53 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:18:53 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:18:53 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:18:53 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:18:53 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:18:53 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:18:53 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:18:53 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:18:53 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:18:53 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:18:53 - main - INFO - main:99 - Creating application...
2025-06-05 19:18:55 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:18:55 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:18:56 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:18:56 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:18:56 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:18:56 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:18:56 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:18:56 - main - INFO - main:102 - Application created successfully
2025-06-05 19:18:56 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:18:56 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:18:56 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:18:59 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:18:59 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:18:59 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:18:59 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:18:59 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:18:59 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:18:59 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:18:59 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 19:18:59 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 19:18:59 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:18:59 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:18:59 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:19:15 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 19:19:16 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 19:19:16 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 19:19:16 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 19:19:16 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 19:19:16 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:19:18 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats_automate.xlsx
2025-06-05 19:19:18 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:19:18 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats_automate.xlsx
2025-06-05 19:19:18 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 53 rows remaining
2025-06-05 19:19:18 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:19:21 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:19:21 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:19:21 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:19:21 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 19:19:21 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 19:19:21 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:19:29 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122_09117.xlsx
2025-06-05 19:19:29 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122-CM Adresse
2025-06-05 19:19:29 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122-Plan Adressage
2025-06-05 19:19:29 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122-Informations Commune
2025-06-05 19:19:29 - core.excel_generator - INFO - _add_data_validations:295 - Data validations added to CM Adresse sheet
2025-06-05 19:19:29 - core.excel_generator - INFO - _create_validation_sheet:321 - Validation sheet created
2025-06-05 19:19:29 - core.excel_generator - INFO - _add_duration_formula:368 - Duration formulas added
2025-06-05 19:19:29 - core.excel_generator - INFO - _add_commune_validations:407 - Commune validations added
2025-06-05 19:19:29 - core.excel_generator - INFO - _add_plan_adressage_validations:436 - Plan Adressage validations added
2025-06-05 19:19:29 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122_09117.xlsx
2025-06-05 19:22:54 - main - INFO - main:107 - Application closed normally
2025-06-05 19:24:30 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:24:30 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:24:30 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:24:30 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:24:30 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:24:30 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:24:30 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:24:30 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:24:30 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:24:30 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:24:30 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:24:30 - main - INFO - main:99 - Creating application...
2025-06-05 19:24:31 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:24:31 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:24:32 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:24:32 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:24:32 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:24:32 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:24:32 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:24:32 - main - INFO - main:102 - Application created successfully
2025-06-05 19:24:32 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:24:32 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:24:32 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:24:34 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:24:34 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:24:34 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:24:34 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:24:34 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:24:34 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:24:34 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:24:35 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 19:24:35 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 19:24:35 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:24:35 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:24:35 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:24:44 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx
2025-06-05 19:24:44 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 65026_Fiabilisation_voies_ARIES ESPENAN_20250522_1512_matrice_globale.xlsx (16 rows)
2025-06-05 19:24:44 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 65026, Commune: ARIES ESPENAN
2025-06-05 19:24:44 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-05 19:24:44 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-05 19:24:44 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:24:46 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:24:46 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:24:46 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:24:46 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 19:24:46 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 62 rows remaining
2025-06-05 19:24:46 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:24:53 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_122(_65026.xlsx
2025-06-05 19:24:53 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-CM Adresse
2025-06-05 19:24:53 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-Plan Adressage
2025-06-05 19:24:53 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-Informations Commune
2025-06-05 19:24:53 - core.excel_generator - INFO - _add_data_validations:295 - Data validations added to CM Adresse sheet
2025-06-05 19:24:53 - core.excel_generator - INFO - _create_validation_sheet:321 - Validation sheet created
2025-06-05 19:24:53 - core.excel_generator - INFO - _add_duration_formula:368 - Duration formulas added
2025-06-05 19:24:53 - core.excel_generator - INFO - _add_commune_validations:407 - Commune validations added
2025-06-05 19:24:53 - core.excel_generator - INFO - _add_plan_adressage_validations:436 - Plan Adressage validations added
2025-06-05 19:24:53 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ARIES ESPENAN_122(_65026.xlsx
2025-06-05 19:25:22 - main - INFO - main:107 - Application closed normally
2025-06-05 19:33:10 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:33:10 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:33:10 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:33:10 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:33:10 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:33:10 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:33:10 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:33:10 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:33:10 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:33:10 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:33:10 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:33:10 - main - INFO - main:99 - Creating application...
2025-06-05 19:33:12 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:33:12 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:33:13 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:33:13 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:33:13 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:33:14 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:33:14 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:33:14 - main - INFO - main:102 - Application created successfully
2025-06-05 19:33:14 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:33:14 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:33:14 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:33:30 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:33:30 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:33:30 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:33:30 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:33:30 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:33:30 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:33:30 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:33:30 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:33:30 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-05 19:33:30 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-05 19:33:30 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:33:30 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:33:39 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx
2025-06-05 19:33:39 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 87193_Fiabilisation_voies_SURDOUX_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-05 19:33:39 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 87193, Commune: SURDOUX
2025-06-05 19:33:39 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 87193
2025-06-05 19:33:39 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SURDOUX
2025-06-05 19:33:39 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:33:42 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:33:42 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:33:42 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:33:42 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-05 19:33:42 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-05 19:33:42 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:33:46 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_122(_87193.xlsx
2025-06-05 19:33:46 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-CM Adresse
2025-06-05 19:33:46 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-Plan Adressage
2025-06-05 19:33:46 - core.excel_generator - INFO - _apply_sheet_styling:228 - Styling applied to sheet: 122(-Informations Commune
2025-06-05 19:33:46 - core.excel_generator - INFO - _add_data_validations:295 - Data validations added to CM Adresse sheet
2025-06-05 19:33:46 - core.excel_generator - INFO - _create_validation_sheet:321 - Validation sheet created
2025-06-05 19:33:46 - core.excel_generator - INFO - _add_duration_formula:368 - Duration formulas added
2025-06-05 19:33:46 - core.excel_generator - INFO - _add_commune_validations:407 - Commune validations added
2025-06-05 19:33:46 - core.excel_generator - INFO - _add_plan_adressage_validations:436 - Plan Adressage validations added
2025-06-05 19:33:46 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SURDOUX_122(_87193.xlsx
2025-06-05 19:37:48 - main - INFO - main:107 - Application closed normally
2025-06-05 19:44:03 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:44:03 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:44:03 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:44:03 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:44:03 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:44:03 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:44:03 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:44:03 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:44:03 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:44:03 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:44:03 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:44:03 - main - INFO - main:99 - Creating application...
2025-06-05 19:44:03 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:44:03 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:44:04 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:44:04 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:44:04 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:44:04 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:44:04 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:44:04 - main - INFO - main:102 - Application created successfully
2025-06-05 19:44:04 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:44:04 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:44:04 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:44:06 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:44:06 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:44:06 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:44:06 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:44:06 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:44:06 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:44:06 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:44:06 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:44:06 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ARIES ESPENAN
2025-06-05 19:44:06 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 65026
2025-06-05 19:44:06 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:44:06 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:44:17 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 69180_Fiabilisation_voies_SAINT ANDRE LA COTE_20250602_1448_matrice_globale.xlsx
2025-06-05 19:44:17 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 69180_Fiabilisation_voies_SAINT ANDRE LA COTE_20250602_1448_matrice_globale.xlsx (19 rows)
2025-06-05 19:44:17 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 69180, Commune: SAINT ANDRE LA COTE
2025-06-05 19:44:17 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 69180
2025-06-05 19:44:17 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SAINT ANDRE LA COTE
2025-06-05 19:44:17 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:44:19 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:44:19 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:44:19 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:44:19 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 39 rows with 'à analyser' and empty key columns
2025-06-05 19:44:19 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 171 rows remaining
2025-06-05 19:44:19 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:44:30 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SAINT ANDRE LA COTE_122è_69180.xlsx
2025-06-05 19:44:31 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122è-CM Adresse
2025-06-05 19:44:31 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122è-Plan Adressage
2025-06-05 19:44:31 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122è-Informations Commune
2025-06-05 19:44:31 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 19:44:31 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 19:44:31 - core.excel_generator - INFO - _add_duration_formula:377 - Duration formulas added
2025-06-05 19:44:31 - core.excel_generator - INFO - _add_commune_validations:433 - Commune validations added
2025-06-05 19:44:31 - core.excel_generator - INFO - _add_plan_adressage_validations:462 - Plan Adressage validations added
2025-06-05 19:44:31 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SAINT ANDRE LA COTE_122è_69180.xlsx
2025-06-05 19:49:54 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:49:54 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:49:54 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:49:54 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:49:54 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:49:54 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:49:54 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:49:54 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:49:54 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:49:54 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:49:54 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:49:54 - main - INFO - main:99 - Creating application...
2025-06-05 19:49:55 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:49:55 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:49:56 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:49:56 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:49:56 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:49:56 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:49:56 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:49:56 - main - INFO - main:102 - Application created successfully
2025-06-05 19:49:56 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:49:56 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:49:56 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:50:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: settings
2025-06-05 19:50:03 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: settings
2025-06-05 19:50:05 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:50:05 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:50:06 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:50:06 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:50:06 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:50:06 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:50:06 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:50:06 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:50:06 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:50:06 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:50:06 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SAINT ANDRE LA COTE
2025-06-05 19:50:06 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 69180
2025-06-05 19:50:06 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:50:06 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:50:15 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 19:50:15 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 19:50:15 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 19:50:15 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 19:50:15 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 19:50:15 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:50:18 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:50:18 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:50:18 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:50:18 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 19:50:18 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 19:50:18 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:50:26 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122222_09117.xlsx
2025-06-05 19:50:26 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122222-CM Adresse
2025-06-05 19:50:26 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122222-Plan Adressage
2025-06-05 19:50:26 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 122222-Informations Commune
2025-06-05 19:50:26 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 19:50:26 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 19:50:26 - core.excel_generator - INFO - _add_duration_formula:372 - Duration formulas added
2025-06-05 19:50:26 - core.excel_generator - INFO - _add_commune_validations:411 - Commune validations added
2025-06-05 19:50:26 - core.excel_generator - INFO - _add_plan_adressage_validations:440 - Plan Adressage validations added
2025-06-05 19:50:26 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_122222_09117.xlsx
2025-06-05 19:55:06 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 19:55:06 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 19:55:06 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 19:55:06 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 19:55:06 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 19:55:06 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 19:55:06 - main - INFO - setup_application:45 - ============================================================
2025-06-05 19:55:06 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 19:55:06 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 19:55:06 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 19:55:06 - main - INFO - setup_application:49 - ============================================================
2025-06-05 19:55:06 - main - INFO - main:99 - Creating application...
2025-06-05 19:55:07 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 19:55:07 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 19:55:08 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 19:55:08 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 19:55:08 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 19:55:08 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 19:55:08 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 19:55:08 - main - INFO - main:102 - Application created successfully
2025-06-05 19:55:08 - main - INFO - main:103 - Starting main loop...
2025-06-05 19:55:08 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 19:55:08 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 19:55:12 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 19:55:12 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 19:55:12 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 19:55:12 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 19:55:12 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 19:55:12 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 19:55:12 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 19:55:12 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 19:55:12 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 19:55:12 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 19:55:12 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 19:55:12 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 19:55:19 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-05 19:55:19 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-05 19:55:19 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-05 19:55:19 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 19:55:19 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 19:55:19 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 19:55:22 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 19:55:22 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 19:55:22 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 19:55:22 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 19:55:22 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-05 19:55:22 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 19:55:28 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_234444_79312.xlsx
2025-06-05 19:55:28 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 234444-CM Adresse
2025-06-05 19:55:28 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 234444-Plan Adressage
2025-06-05 19:55:28 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 234444-Informations Commune
2025-06-05 19:55:28 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 19:55:28 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 19:55:28 - core.excel_generator - INFO - _add_duration_formula:372 - Duration formulas added
2025-06-05 19:55:28 - core.excel_generator - INFO - _add_commune_validations:411 - Commune validations added
2025-06-05 19:55:28 - core.excel_generator - INFO - _add_plan_adressage_validations:440 - Plan Adressage validations added
2025-06-05 19:55:29 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_234444_79312.xlsx
2025-06-05 20:06:30 - main - INFO - main:107 - Application closed normally
2025-06-05 20:06:48 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 20:06:48 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 20:06:48 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 20:06:48 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 20:06:48 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 20:06:48 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 20:06:48 - main - INFO - setup_application:45 - ============================================================
2025-06-05 20:06:48 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 20:06:48 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 20:06:48 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 20:06:48 - main - INFO - setup_application:49 - ============================================================
2025-06-05 20:06:48 - main - INFO - main:99 - Creating application...
2025-06-05 20:06:49 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 20:06:49 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 20:06:50 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 20:06:50 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 20:06:50 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 20:06:50 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 20:06:50 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 20:06:50 - main - INFO - main:102 - Application created successfully
2025-06-05 20:06:50 - main - INFO - main:103 - Starting main loop...
2025-06-05 20:06:50 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 20:06:50 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 20:06:51 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 20:06:51 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 20:06:51 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 20:06:51 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 20:06:51 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 20:06:51 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 20:06:51 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 20:06:51 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 20:06:51 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 20:06:51 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 20:06:51 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 20:06:51 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 20:06:56 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-05 20:06:56 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-05 20:06:56 - ui.components.generation - INFO - reset:196 - Generation section reset
2025-06-05 20:06:56 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-05 20:06:56 - ui.modules.suivi_generator_module - INFO - _reset_module:506 - Module reset successfully
2025-06-05 20:07:08 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx
2025-06-05 20:07:08 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 79312_Fiabilisation_voies_SELIGNE_20250602_0959_matrice_globale.xlsx (18 rows)
2025-06-05 20:07:08 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 79312, Commune: SELIGNE
2025-06-05 20:07:08 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 20:07:08 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 20:07:08 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 20:07:10 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 20:07:10 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 20:07:10 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 20:07:10 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 20:07:10 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 82 rows remaining
2025-06-05 20:07:10 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 20:07:16 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_0_79312.xlsx
2025-06-05 20:07:16 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-CM Adresse
2025-06-05 20:07:16 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-Plan Adressage
2025-06-05 20:07:16 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-Informations Commune
2025-06-05 20:07:16 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 20:07:16 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 20:07:16 - core.excel_generator - INFO - _add_duration_formula:372 - Duration formulas added
2025-06-05 20:07:16 - core.excel_generator - INFO - _add_commune_validations:411 - Commune validations added
2025-06-05 20:07:16 - core.excel_generator - INFO - _add_plan_adressage_validations:440 - Plan Adressage validations added
2025-06-05 20:07:17 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_0_79312.xlsx
2025-06-05 20:23:04 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_0_79312.xlsx
2025-06-05 20:23:04 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-CM Adresse
2025-06-05 20:23:04 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-Plan Adressage
2025-06-05 20:23:04 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 0-Informations Commune
2025-06-05 20:23:04 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 20:23:04 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 20:23:04 - core.excel_generator - INFO - _add_duration_formula:372 - Duration formulas added
2025-06-05 20:23:04 - core.excel_generator - INFO - _add_commune_validations:411 - Commune validations added
2025-06-05 20:23:04 - core.excel_generator - INFO - _add_plan_adressage_validations:440 - Plan Adressage validations added
2025-06-05 20:23:04 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_SELIGNE_0_79312.xlsx
2025-06-05 20:31:02 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 20:31:02 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 20:31:02 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 20:31:02 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 20:31:02 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 20:31:02 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 20:31:02 - main - INFO - setup_application:45 - ============================================================
2025-06-05 20:31:02 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 20:31:02 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 20:31:02 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 20:31:02 - main - INFO - setup_application:49 - ============================================================
2025-06-05 20:31:02 - main - INFO - main:99 - Creating application...
2025-06-05 20:31:02 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 20:31:02 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 20:31:03 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 20:31:03 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 20:31:03 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 20:31:03 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 20:31:03 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 20:31:03 - main - INFO - main:102 - Application created successfully
2025-06-05 20:31:03 - main - INFO - main:103 - Starting main loop...
2025-06-05 20:31:03 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 20:31:03 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 20:31:15 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 20:31:15 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 20:31:15 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 20:31:15 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 20:31:15 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 20:31:15 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 20:31:15 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 20:31:15 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 20:31:15 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: SELIGNE
2025-06-05 20:31:15 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 79312
2025-06-05 20:31:15 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 20:31:15 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 20:31:18 - ui.components.file_import - INFO - reset:257 - File import section reset
2025-06-05 20:31:18 - ui.components.project_info - INFO - clear_fields:221 - Project info fields cleared
2025-06-05 20:31:18 - ui.components.generation - INFO - reset:196 - Generation section reset
2025-06-05 20:31:18 - utils.session_manager - INFO - clear_session:141 - Session cleared
2025-06-05 20:31:18 - ui.modules.suivi_generator_module - INFO - _reset_module:506 - Module reset successfully
2025-06-05 20:31:25 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 20:31:25 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 20:31:25 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 20:31:25 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 20:31:25 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 20:31:25 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 20:31:27 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 20:31:27 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 20:31:27 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 20:31:27 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 20:31:27 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 20:31:27 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 20:31:32 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1_09117.xlsx
2025-06-05 20:31:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1-CM Adresse
2025-06-05 20:31:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1-Plan Adressage
2025-06-05 20:31:32 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1-Informations Commune
2025-06-05 20:31:32 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 20:31:32 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 20:31:32 - core.excel_generator - INFO - _add_duration_formula:373 - Duration formulas added
2025-06-05 20:31:32 - core.excel_generator - INFO - _add_commune_validations:412 - Commune validations added
2025-06-05 20:31:32 - core.excel_generator - INFO - _add_plan_adressage_validations:441 - Plan Adressage validations added
2025-06-05 20:31:32 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1_09117.xlsx
2025-06-05 20:36:04 - main - INFO - main:107 - Application closed normally
2025-06-05 20:36:24 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250605.log
2025-06-05 20:36:24 - root - INFO - setup_logging:79 - ==================================================
2025-06-05 20:36:24 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-05 20:36:24 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-05 20:36:24 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-05 20:36:24 - root - INFO - setup_logging:83 - ==================================================
2025-06-05 20:36:24 - main - INFO - setup_application:45 - ============================================================
2025-06-05 20:36:24 - main - INFO - setup_application:46 - Starting Traitement automatisé MOAI et QGis
2025-06-05 20:36:24 - main - INFO - setup_application:47 - Version: 2.0.0
2025-06-05 20:36:24 - main - INFO - setup_application:48 - Author: Sofrecom Tunisie - Equipe BLI
2025-06-05 20:36:24 - main - INFO - setup_application:49 - ============================================================
2025-06-05 20:36:24 - main - INFO - main:99 - Creating application...
2025-06-05 20:36:24 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-05 20:36:24 - ui.main_window - INFO - _setup_window:72 - Main window configured
2025-06-05 20:36:25 - ui.navigation - INFO - register_module:158 - Registered module: Générateur Suivi (suivi_generator)
2025-06-05 20:36:25 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: home
2025-06-05 20:36:25 - ui.main_window - INFO - _set_window_icon:140 - Window icon set successfully
2025-06-05 20:36:25 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: home
2025-06-05 20:36:25 - ui.main_window - INFO - _setup_navigation:101 - Navigation system initialized
2025-06-05 20:36:25 - main - INFO - main:102 - Application created successfully
2025-06-05 20:36:25 - main - INFO - main:103 - Starting main loop...
2025-06-05 20:36:25 - ui.main_window - INFO - run:156 - Starting application main loop
2025-06-05 20:36:25 - ui.main_window - INFO - _post_init:118 - Main window initialization complete
2025-06-05 20:36:39 - ui.home_screen - INFO - _open_suivi_generator:328 - User clicked Suivi Generator button
2025-06-05 20:36:39 - ui.navigation - INFO - navigate_to:179 - Attempting to navigate to: suivi_generator
2025-06-05 20:36:39 - ui.navigation - INFO - _load_module:237 - Loading module: suivi_generator (Générateur Suivi)
2025-06-05 20:36:39 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-05 20:36:39 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-05 20:36:39 - ui.navigation - INFO - _load_module:262 - Module suivi_generator created and loaded successfully
2025-06-05 20:36:39 - ui.navigation - INFO - navigate_to:214 - Successfully navigated to: suivi_generator
2025-06-05 20:36:40 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-05 20:36:40 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 20:36:40 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 20:36:40 - ui.modules.suivi_generator_module - INFO - _restore_session:477 - Session restored successfully
2025-06-05 20:36:40 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-05 20:36:46 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx
2025-06-05 20:36:47 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 09117_Fiabilisation_voies_ESPLAS_20250526_1412_matrice_globale.xlsx (17 rows)
2025-06-05 20:36:47 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 09117, Commune: ESPLAS
2025-06-05 20:36:47 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 09117
2025-06-05 20:36:47 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: ESPLAS
2025-06-05 20:36:47 - ui.modules.suivi_generator_module - INFO - on_success:331 - MOAI file processed successfully
2025-06-05 20:36:50 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-05 20:36:50 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-05 20:36:50 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-05 20:36:50 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 40 rows with 'à analyser' and empty key columns
2025-06-05 20:36:50 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 74 rows remaining
2025-06-05 20:36:50 - ui.modules.suivi_generator_module - INFO - on_success:353 - QGis file processed successfully
2025-06-05 20:36:59 - ui.components.generation - INFO - get_save_path:217 - Save path selected: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1é_09117.xlsx
2025-06-05 20:36:59 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1é-CM Adresse
2025-06-05 20:36:59 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1é-Plan Adressage
2025-06-05 20:36:59 - core.excel_generator - INFO - _apply_sheet_styling:229 - Styling applied to sheet: 1é-Informations Commune
2025-06-05 20:36:59 - core.excel_generator - INFO - _add_data_validations:296 - Data validations added to CM Adresse sheet
2025-06-05 20:36:59 - core.excel_generator - INFO - _create_validation_sheet:322 - Validation sheet created
2025-06-05 20:36:59 - core.excel_generator - INFO - _add_duration_formula:373 - Duration formulas added
2025-06-05 20:36:59 - core.excel_generator - INFO - _add_commune_validations:412 - Commune validations added
2025-06-05 20:36:59 - core.excel_generator - INFO - _add_plan_adressage_validations:441 - Plan Adressage validations added
2025-06-05 20:36:59 - core.excel_generator - INFO - generate_excel_file:76 - Excel file generated successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Suivi_ESPLAS_1é_09117.xlsx
2025-06-05 20:37:00 - main - INFO - main:107 - Application closed normally
