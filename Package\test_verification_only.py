"""
Test uniquement de la fonction de vérification des fichiers
"""

import os
import sys
from pathlib import Path

# Configuration
APP_NAME = "SofreTrack Pro"
APP_VERSION = "3.0"
MAIN_SCRIPT = "../src/main.py"
ICON_ICO = "../Icone_App_Sharp.ico"

def verify_required_files():
    """Vérifie que tous les fichiers requis sont présents"""
    print("Verification des fichiers requis...")
    
    required_files = {
        "Script principal": MAIN_SCRIPT,
        "Icône Sharp (.ico)": ICON_ICO,
        "Icône PNG": "../Icone_App.png",
        "Logo Sofrecom": "../logo_Sofrecom.png"
    }
    
    all_present = True
    for description, file_path in required_files.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"   ✅ {description}: {file_path} ({file_size:.1f} KB)")
        else:
            print(f"   ❌ {description}: {file_path} - MANQUANT")
            all_present = False
    
    if all_present:
        print("   Tous les fichiers requis sont présents")
    else:
        print("   ERREUR: Des fichiers requis sont manquants")
    
    return all_present

if __name__ == "__main__":
    print("TEST DE VERIFICATION DES FICHIERS")
    print("=" * 40)
    
    success = verify_required_files()
    
    if success:
        print("\n🎉 VERIFICATION REUSSIE!")
        print("Le script de packaging peut être exécuté.")
    else:
        print("\n❌ VERIFICATION ECHOUEE!")
        print("Corrigez les fichiers manquants avant de continuer.")
