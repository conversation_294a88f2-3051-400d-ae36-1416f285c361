2025-06-09 03:13:27 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 03:13:27 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 03:13:27 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 03:13:27 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 03:13:27 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 03:13:27 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 03:13:27 - ui.splash_screen - INFO - _init_environment:360 - ============================================================
2025-06-09 03:13:27 - ui.splash_screen - INFO - _init_environment:361 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 03:13:27 - ui.splash_screen - INFO - _init_environment:362 - Version: 2.1
2025-06-09 03:13:27 - ui.splash_screen - INFO - _init_environment:363 - Author: Equipe BLI
2025-06-09 03:13:27 - ui.splash_screen - INFO - _init_environment:364 - ============================================================
2025-06-09 03:13:29 - __main__ - INFO - start_main_application:97 - Creating main application...
2025-06-09 03:13:30 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 03:13:30 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 03:13:30 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI259722\logo_Sofrecom.png
2025-06-09 03:13:30 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 03:13:30 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 03:13:30 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 03:13:30 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:13:30 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 03:13:31 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:13:31 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 03:13:31 - __main__ - INFO - start_main_application:100 - Application created successfully
2025-06-09 03:13:31 - __main__ - INFO - start_main_application:101 - Starting main loop...
2025-06-09 03:13:31 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 03:13:31 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 03:13:34 - __main__ - INFO - start_main_application:105 - Application closed normally
2025-06-09 03:13:55 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 03:13:55 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 03:13:55 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 03:13:55 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 03:13:55 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 03:13:55 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 03:13:55 - ui.splash_screen - INFO - _init_environment:360 - ============================================================
2025-06-09 03:13:55 - ui.splash_screen - INFO - _init_environment:361 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 03:13:55 - ui.splash_screen - INFO - _init_environment:362 - Version: 2.1
2025-06-09 03:13:55 - ui.splash_screen - INFO - _init_environment:363 - Author: Equipe BLI
2025-06-09 03:13:55 - ui.splash_screen - INFO - _init_environment:364 - ============================================================
2025-06-09 03:13:58 - __main__ - INFO - start_main_application:97 - Creating main application...
2025-06-09 03:13:58 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 03:13:58 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 03:13:58 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\MYPROG~1\Windows\Temp\_MEI196322\logo_Sofrecom.png
2025-06-09 03:13:58 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 03:13:58 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 03:13:58 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 03:13:58 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:13:58 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 03:13:59 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:13:59 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 03:13:59 - __main__ - INFO - start_main_application:100 - Application created successfully
2025-06-09 03:13:59 - __main__ - INFO - start_main_application:101 - Starting main loop...
2025-06-09 03:13:59 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 03:13:59 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 03:14:02 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 03:14:02 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 03:14:02 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 03:14:02 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:14:02 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 03:14:02 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 03:14:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 03:14:03 - ui.modules.suivi_global_module - ERROR - _initialize_optional_features:109 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\suivi_global'
2025-06-09 03:14:03 - ui.modules.suivi_global_module - INFO - _load_existing_communes:537 - Loaded 7 existing communes for comparison
2025-06-09 03:14:03 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: ARIES ESPENAN
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: ESPLAS
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: MONTCHALONS
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: SELIGNE
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: SURDOUX
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _process_commune_folders:665 - Processed commune: TUNIS
2025-06-09 03:14:04 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:559 - Analysis: 0 new communes, 7 communes to update
2025-06-09 03:14:06 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:947 - Updating existing global Excel file
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:06 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:963 - Applied date formatting to existing Suivi Tickets data
2025-06-09 03:14:06 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:976 - Applied date formatting to existing Traitement CMS Adr data
2025-06-09 03:14:06 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:989 - Applied date formatting to existing Traitement PA data
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1661 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1452 - Formatted INSEE columns as text in sheet: Suivi Tickets
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in ESPLAS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in MONTCHALONS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in SELIGNE: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in SURDOUX: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1175 - CM Adresse columns in TUNIS: ['Nom commune', 'Insee', 'ID Tache', 'Voie demandé', 'Motif Voie', 'CODE RIVOLI', 'GPS (X,Y)', 'Centre/Zone', 'Status PC', 'Descriptif Commentaire', 'Collaborateur', 'Date affectation', 'Date traitement', 'Date livraison', 'Durée', 'STATUT Ticket']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page2_cm_adresse_data:1216 - Applied date formatting to new CM Adresse data: 98 rows
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1758 - Applying aggressive date formatting to Traitement CMS Adr for columns: ['Date affectation', 'Date traitement', 'Date livraison']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1763 - Processing column 'Date affectation' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1763 - Processing column 'Date traitement' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1763 - Processing column 'Date livraison' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1452 - Formatted INSEE columns as text in sheet: Traitement CMS Adr
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in ARIES ESPENAN: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in ESPLAS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in MONTCHALONS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in SAINT ANDRE LA COTE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in SELIGNE: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in SURDOUX: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1289 - Plan Adressage columns in TUNIS: ['Nom commune', 'Insee', 'Num Dossier Site', 'Num Voie Site', 'Comp Voie Site', 'Batiment IMB', 'Libelle Voie Site', 'Même Adresse', 'Motif', 'Numero Voie BAN', 'Repondant Voie BAN', 'Libelle Voie BAN', 'Adresse BAN', 'Collaborateur', 'Date traitement', 'Durée']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _create_page3_plan_adressage_data:1330 - Applied date formatting to new Plan Adressage data: 553 rows
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1758 - Applying aggressive date formatting to Traitement PA for columns: ['Date traitement']
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _force_date_formatting_for_pages_2_3:1763 - Processing column 'Date traitement' in Traitement PA
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1718 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 03:14:07 - ui.modules.suivi_global_module - INFO - _format_insee_columns_as_text:1452 - Formatted INSEE columns as text in sheet: Traitement PA
2025-06-09 03:14:08 - ui.modules.suivi_global_module - INFO - _format_sheet:1541 - Formatting applied to sheet: Suivi Tickets
2025-06-09 03:14:08 - ui.modules.suivi_global_module - INFO - _format_sheet:1541 - Formatting applied to sheet: Traitement CMS Adr
2025-06-09 03:14:08 - ui.modules.suivi_global_module - INFO - _format_sheet:1541 - Formatting applied to sheet: Traitement PA
2025-06-09 03:14:08 - ui.modules.suivi_global_module - INFO - _format_global_excel:1478 - Global Excel file formatted successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 03:14:08 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:1002 - Global Excel file updated: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 03:14:11 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 03:14:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 03:14:11 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 03:14:11 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 03:14:11 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 03:14:11 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 03:14:11 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 03:14:11 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 03:14:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 03:14:11 - ui.modules.team_stats_module - ERROR - _initialize_optional_features:120 - Error initializing optional features: [WinError 3] Le chemin d’accès spécifié est introuvable: 'sessions\\team_stats'
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _load_global_data:704 - Loaded sheet 'Suivi Tickets' with 7 rows
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _load_global_data:704 - Loaded sheet 'Traitement CMS Adr' with 98 rows
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _load_global_data:704 - Loaded sheet 'Traitement PA' with 553 rows
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1265 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1266 - DataFrame shape: (7, 18)
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1287 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1288 - Has 'Date Livraison' column: True
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1347 - Communes traitées ce mois: 0
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1348 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1253 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:1092 - Analyzed statistics for 1 collaborators
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _update_overview_display:1410 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _update_overview_display:1411 - Communes traitées mois courant: 0
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _update_overview_display:1412 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _update_export_filters:3172 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 03:14:14 - ui.modules.team_stats_module - INFO - _load_global_data:734 - Global data loaded and analyzed successfully
2025-06-09 03:14:19 - ui.modules.team_stats_module - INFO - _reset_module:314 - Module reset successfully
2025-06-09 03:14:20 - __main__ - INFO - start_main_application:105 - Application closed normally
