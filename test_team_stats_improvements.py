#!/usr/bin/env python3
"""
Test script for Team Stats module file access improvements.
"""

import sys
import os
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from utils.file_utils import check_file_access, is_excel_file_open

def test_team_stats_file_access():
    """Test the file access checking functionality for Team Stats module."""
    print("🧪 Testing Team Stats Module File Access Improvements")
    print("=" * 60)
    
    # Simulate the global Excel file path
    from config.constants import TeamsConfig
    teams_folder_path = TeamsConfig.get_global_teams_path()
    global_excel_filename = "Suivis Global Tickets CMS Adr_PA.xlsx"
    global_file_path = os.path.join(teams_folder_path, global_excel_filename)
    
    print(f"\n📁 Chemin du fichier global testé:")
    print(f"   {global_file_path}")
    
    # Test 1: Check if global file exists
    print(f"\n1. Vérification de l'existence du fichier:")
    if os.path.exists(global_file_path):
        print(f"   ✅ Le fichier existe")
        
        # Test 2: Check file access for reading (stats loading)
        print(f"\n2. Test d'accès en lecture (chargement des statistiques):")
        access_result = check_file_access(global_file_path, 'r')
        print(f"   Accessible: {access_result['accessible']}")
        print(f"   Type d'erreur: {access_result['error_type']}")
        if access_result['user_message']:
            print(f"   Message utilisateur: {access_result['user_message']}")
        if access_result['suggestions']:
            print(f"   Suggestions: {access_result['suggestions']}")
        
        # Test 3: Check if file appears to be open
        print(f"\n3. Test de détection d'ouverture Excel:")
        is_open = is_excel_file_open(global_file_path)
        print(f"   Fichier ouvert dans Excel: {is_open}")
        
        # Test 4: Check file access for writing (export functionality)
        print(f"\n4. Test d'accès en écriture (export Excel):")
        access_result_write = check_file_access(global_file_path, 'w')
        print(f"   Accessible en écriture: {access_result_write['accessible']}")
        print(f"   Type d'erreur: {access_result_write['error_type']}")
        if access_result_write['user_message']:
            print(f"   Message utilisateur: {access_result_write['user_message']}")
        
    else:
        print(f"   ⚠️ Le fichier n'existe pas encore")
        print(f"   💡 Exécutez d'abord le module 'Suivi Global Tickets' pour créer le fichier")
    
    # Test 5: Test export file access simulation
    print(f"\n5. Test d'accès pour fichier d'export:")
    export_file = "test_export_ctj.xlsx"
    
    # Create a temporary file to test
    try:
        with open(export_file, 'w') as f:
            f.write("test")
        
        access_result_export = check_file_access(export_file, 'w')
        print(f"   Fichier d'export accessible: {access_result_export['accessible']}")
        print(f"   Type d'erreur: {access_result_export['error_type']}")
        
        # Clean up
        os.remove(export_file)
        
    except Exception as e:
        print(f"   Erreur lors du test d'export: {e}")
    
    print(f"\n✅ Tests des améliorations du module Statistiques Équipe terminés!")
    print(f"\n💡 Fonctionnalités ajoutées:")
    print(f"   • Vérification d'accès au fichier avant chargement des données")
    print(f"   • Dialogue convivial en cas de fichier en cours d'utilisation")
    print(f"   • Indicateur de statut du fichier dans l'interface")
    print(f"   • Vérification d'accès lors de l'export Excel")
    print(f"   • Messages d'erreur améliorés et suggestions d'action")
    
    print(f"\n🎯 Pour tester en conditions réelles:")
    print(f"   1. Ouvrez le fichier global dans Excel")
    print(f"   2. Essayez de charger les données dans le module Statistiques Équipe")
    print(f"   3. Vous devriez voir le dialogue convivial avec instructions")
    print(f"   4. Fermez Excel et réessayez - cela devrait fonctionner")

if __name__ == "__main__":
    test_team_stats_file_access()
